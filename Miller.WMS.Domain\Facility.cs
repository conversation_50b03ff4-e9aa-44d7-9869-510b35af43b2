﻿using Miller.WMS.Domain;

namespace Miller.WMS.Domain;

public class Facility
{
    public int Id { get; set; }
    public string Name { get; set; }
    public int OrganizationId { get; set; }
    public Organization Organization { get; set; }
    public ICollection<UserFacilityRole> UserFacilityRoles { get; set; }

    public Facility()
    {
        Name = string.Empty;
        Organization = new Organization();
        UserFacilityRoles = new List<UserFacilityRole>();
    }
}