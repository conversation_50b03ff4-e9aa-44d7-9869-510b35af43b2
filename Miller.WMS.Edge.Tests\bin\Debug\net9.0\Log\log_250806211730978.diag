2025-08-06T21:17:30.9848171+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Version: 1.7.3+5abdae1f2e07071c4e81b27ac262f241708ec3cf
2025-08-06T21:17:30.9940367+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging mode: asynchronous
2025-08-06T21:17:30.9949501+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging level: Information
2025-08-06T21:17:30.9949557+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION CreateBuilderAsync entry time: 21:17:30.961
2025-08-06T21:17:30.9990717+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION PID: 40364
2025-08-06T21:17:30.9991117+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime information: win-x64 - .NET 9.0.7
2025-08-06T21:17:30.9991218+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime location: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.7\System.Private.CoreLib.dll
2025-08-06T21:17:30.9991310+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION IsDynamicCodeSupported: True
2025-08-06T21:17:30.9992896+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Test module: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Miller.WMS.Edge.Tests.dll
2025-08-06T21:17:30.9994406+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Command line arguments: '--server --diagnostic --diagnostic-verbosity Information --diagnostic-output-directory C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Log --results-directory C:\_\Miller_Github\Miller.WMS\TestResults --client-port 62841'
2025-08-06T21:17:31.0005181+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION TESTINGPLATFORM_DEFAULT_HANG_TIMEOUT: ''
2025-08-06T21:17:31.0099213+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting RegisterEnvironmentVariablesConfigurationSource: 'True'
2025-08-06T21:17:31.0199661+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting PlatformExitProcessOnUnhandledException: 'False', config file: False environment variable: 
2025-08-06T21:17:31.2150953+00:00 Microsoft.Testing.Platform.Requests.TestHostTestFrameworkInvoker INFORMATION Test framework UID: '30ea7c6e-dd24-4152-a360-1387158cd41d' Version: '3.0.0' DisplayName: 'xUnit.net v3 Microsoft.Testing.Platform test framework' Description: 'xUnit.net v3 Microsoft.Testing.Platform test framework'
2025-08-06T21:17:31.2178221+00:00 xUnit.net INFORMATION xUnit.net v3 In-Process Runner v3.0.0+d0213fc4e5 (64-bit .NET 9.0.7)
2025-08-06T21:17:31.2550394+00:00 xUnit.net INFORMATION   Discovering: Miller.WMS.Edge.Tests
2025-08-06T21:17:31.2886355+00:00 xUnit.net INFORMATION   Discovered:  Miller.WMS.Edge.Tests
2025-08-06T21:17:31.3129122+00:00 xUnit.net INFORMATION   Starting:    Miller.WMS.Edge.Tests
2025-08-06T21:17:31.4884442+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.SimpleCDCTests.CDCService_CanBeInstantiated [FAIL]
2025-08-06T21:17:31.4889433+00:00 xUnit.net INFORMATION       System.NotImplementedException : Mock context for DI only
2025-08-06T21:17:31.4893417+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:17:31.4946293+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\SimpleCDCTests.cs(47,0): at Miller.WMS.Edge.Tests.SimpleCDCTests.<>c.<CDCService_CanBeInstantiated>b__0_1(IServiceProvider sp)
2025-08-06T21:17:31.4946565+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
2025-08-06T21:17:31.4946626+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
2025-08-06T21:17:31.4946664+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
2025-08-06T21:17:31.4946695+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
2025-08-06T21:17:31.4946784+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
2025-08-06T21:17:31.4946817+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
2025-08-06T21:17:31.4946851+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
2025-08-06T21:17:31.4946885+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
2025-08-06T21:17:31.4946915+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
2025-08-06T21:17:31.4946948+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
2025-08-06T21:17:31.4947075+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
2025-08-06T21:17:31.4947111+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
2025-08-06T21:17:31.4947132+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type serviceType)
2025-08-06T21:17:31.4947214+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetService[T](IServiceProvider provider)
2025-08-06T21:17:31.4947267+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\SimpleCDCTests.cs(55,0): at Miller.WMS.Edge.Tests.SimpleCDCTests.CDCService_CanBeInstantiated()
2025-08-06T21:17:31.4947295+00:00 xUnit.net INFORMATION            at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
2025-08-06T21:17:31.4947317+00:00 xUnit.net INFORMATION            at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)
2025-08-06T21:19:31.7047063+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.ApiTests.GetApiResourceRootReturnsOkStatusCode [FAIL]
2025-08-06T21:19:31.7047476+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7047503+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7047754+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7047831+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7047899+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7048082+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:31.7051649+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.ApiTests.GetPingEndpointReturnsTrue [FAIL]
2025-08-06T21:19:31.7051771+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7051796+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7051899+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7051939+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7051965+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7052111+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:31.7053617+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CDCTests.CDCServiceStartsSuccessfully [FAIL]
2025-08-06T21:19:31.7053731+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7053754+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7053824+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7053857+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7053879+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7054001+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:31.7055067+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CDCTests.ElasticsearchServiceIsHealthy [FAIL]
2025-08-06T21:19:31.7055130+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7055161+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7055223+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7055290+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7055314+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7055410+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:31.7056220+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CDCTests.InitialSyncCompletedSuccessfully [FAIL]
2025-08-06T21:19:31.7056365+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7056465+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7056544+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7056666+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7056692+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7057331+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:31.7059713+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CDCTests.OrganizationDataSyncedCorrectly [FAIL]
2025-08-06T21:19:31.7059800+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7059824+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7059908+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7059943+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7059968+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7060139+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:31.7061422+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CDCTests.DatabaseChangesAreSyncedToElasticsearch [FAIL]
2025-08-06T21:19:31.7061484+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7061506+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7061568+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7061599+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7061641+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7061737+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:31.7062628+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.DataServiceCompletedSuccessfully [FAIL]
2025-08-06T21:19:31.7062701+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7062723+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7062816+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7062846+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7062869+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7062967+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:31.7063728+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.DatabaseServiceHealthy [FAIL]
2025-08-06T21:19:31.7063783+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7063804+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7063876+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7063905+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7063926+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7064027+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:31.7064763+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.AllServicesHealthy [FAIL]
2025-08-06T21:19:31.7064813+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7064852+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7064907+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7064932+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7064953+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7065075+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:31.7065959+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.DataServiceLogsIndicateSuccess [FAIL]
2025-08-06T21:19:31.7066010+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7066032+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7066088+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7066129+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7066152+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7066243+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:31.7067026+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.WebTests.GetWebResourceRootReturnsOkStatusCode [FAIL]
2025-08-06T21:19:31.7067081+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:19:31.7067102+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:19:31.7067182+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:19:31.7067213+00:00 xUnit.net INFORMATION         
2025-08-06T21:19:31.7067236+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:19:31.7067335+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:19:48.2421510+00:00 xUnit.net INFORMATION   Finished:    Miller.WMS.Edge.Tests
2025-08-06T21:19:48.2473374+00:00 xUnit.net INFORMATION === TEST EXECUTION SUMMARY ===
2025-08-06T21:19:48.2537912+00:00 xUnit.net INFORMATION    Miller.WMS.Edge.Tests  Total: 15, Errors: 0, Failed: 13, Skipped: 0, Not Run: 0, Time: 136.935s
