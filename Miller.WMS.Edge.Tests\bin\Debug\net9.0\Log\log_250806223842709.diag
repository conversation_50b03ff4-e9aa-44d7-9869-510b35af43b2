2025-08-06T22:38:42.7156090+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Version: 1.7.3+5abdae1f2e07071c4e81b27ac262f241708ec3cf
2025-08-06T22:38:42.7251943+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging mode: asynchronous
2025-08-06T22:38:42.7261326+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging level: Information
2025-08-06T22:38:42.7261376+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION CreateBuilderAsync entry time: 22:38:42.693
2025-08-06T22:38:42.7297254+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION PID: 36404
2025-08-06T22:38:42.7297498+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime information: win-x64 - .NET 9.0.7
2025-08-06T22:38:42.7297590+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime location: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.7\System.Private.CoreLib.dll
2025-08-06T22:38:42.7297672+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION IsDynamicCodeSupported: True
2025-08-06T22:38:42.7298031+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Test module: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Miller.WMS.Edge.Tests.dll
2025-08-06T22:38:42.7298707+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Command line arguments: '--server --diagnostic --diagnostic-verbosity Information --diagnostic-output-directory C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Log --results-directory C:\_\Miller_Github\Miller.WMS\TestResults --client-port 61516'
2025-08-06T22:38:42.7308958+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION TESTINGPLATFORM_DEFAULT_HANG_TIMEOUT: ''
2025-08-06T22:38:42.7399608+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting RegisterEnvironmentVariablesConfigurationSource: 'True'
2025-08-06T22:38:42.7501618+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting PlatformExitProcessOnUnhandledException: 'False', config file: False environment variable: 
2025-08-06T22:38:42.9189505+00:00 Microsoft.Testing.Platform.Requests.TestHostTestFrameworkInvoker INFORMATION Test framework UID: '30ea7c6e-dd24-4152-a360-1387158cd41d' Version: '3.0.0' DisplayName: 'xUnit.net v3 Microsoft.Testing.Platform test framework' Description: 'xUnit.net v3 Microsoft.Testing.Platform test framework'
2025-08-06T22:38:42.9220209+00:00 xUnit.net INFORMATION xUnit.net v3 In-Process Runner v3.0.0+d0213fc4e5 (64-bit .NET 9.0.7)
