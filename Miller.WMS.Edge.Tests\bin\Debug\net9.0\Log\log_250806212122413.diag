2025-08-06T21:21:22.4183330+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Version: 1.7.3+5abdae1f2e07071c4e81b27ac262f241708ec3cf
2025-08-06T21:21:22.4271199+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging mode: asynchronous
2025-08-06T21:21:22.4279888+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging level: Information
2025-08-06T21:21:22.4279931+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION CreateBuilderAsync entry time: 21:21:22.397
2025-08-06T21:21:22.4311631+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION PID: 41488
2025-08-06T21:21:22.4311958+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime information: win-x64 - .NET 9.0.7
2025-08-06T21:21:22.4312081+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime location: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.7\System.Private.CoreLib.dll
2025-08-06T21:21:22.4312167+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION IsDynamicCodeSupported: True
2025-08-06T21:21:22.4313360+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Test module: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Miller.WMS.Edge.Tests.dll
2025-08-06T21:21:22.4314850+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Command line arguments: '--server --diagnostic --diagnostic-verbosity Information --diagnostic-output-directory C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Log --results-directory C:\_\Miller_Github\Miller.WMS\TestResults --client-port 63479'
2025-08-06T21:21:22.4325118+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION TESTINGPLATFORM_DEFAULT_HANG_TIMEOUT: ''
2025-08-06T21:21:22.4410957+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting RegisterEnvironmentVariablesConfigurationSource: 'True'
2025-08-06T21:21:22.4518928+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting PlatformExitProcessOnUnhandledException: 'False', config file: False environment variable: 
2025-08-06T21:21:22.6091879+00:00 Microsoft.Testing.Platform.Requests.TestHostTestFrameworkInvoker INFORMATION Test framework UID: '30ea7c6e-dd24-4152-a360-1387158cd41d' Version: '3.0.0' DisplayName: 'xUnit.net v3 Microsoft.Testing.Platform test framework' Description: 'xUnit.net v3 Microsoft.Testing.Platform test framework'
2025-08-06T21:21:22.6119761+00:00 xUnit.net INFORMATION xUnit.net v3 In-Process Runner v3.0.0+d0213fc4e5 (64-bit .NET 9.0.7)
2025-08-06T21:21:22.6433173+00:00 xUnit.net INFORMATION   Discovering: Miller.WMS.Edge.Tests
2025-08-06T21:21:22.6781968+00:00 xUnit.net INFORMATION   Discovered:  Miller.WMS.Edge.Tests
2025-08-06T21:21:22.6941325+00:00 xUnit.net INFORMATION   Starting:    Miller.WMS.Edge.Tests
2025-08-06T21:23:22.9621130+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.ApiTests.GetApiResourceRootReturnsOkStatusCode [FAIL]
2025-08-06T21:23:22.9627233+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:23:22.9627385+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:23:22.9633517+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:23:22.9639470+00:00 xUnit.net INFORMATION         
2025-08-06T21:23:22.9639617+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:23:22.9639941+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:23:22.9704169+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.ApiTests.GetPingEndpointReturnsTrue [FAIL]
2025-08-06T21:23:22.9704421+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T21:23:22.9704462+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T21:23:22.9704738+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T21:23:22.9704792+00:00 xUnit.net INFORMATION         
2025-08-06T21:23:22.9704822+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T21:23:22.9704982+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T21:23:38.4912206+00:00 xUnit.net INFORMATION   Finished:    Miller.WMS.Edge.Tests
2025-08-06T21:23:38.4983137+00:00 xUnit.net INFORMATION === TEST EXECUTION SUMMARY ===
2025-08-06T21:23:38.5076527+00:00 xUnit.net INFORMATION    Miller.WMS.Edge.Tests  Total: 2, Errors: 0, Failed: 2, Skipped: 0, Not Run: 0, Time: 135.800s
