{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Miller.WMS.Edge.AppHost/1.0.0": {"dependencies": {"Aspire.Dashboard.Sdk.win-x64": "9.4.0", "Aspire.Hosting.AppHost": "9.4.0", "Aspire.Hosting.Azure.AppConfiguration": "9.4.0", "Aspire.Hosting.Azure.AppContainers": "9.4.0", "Aspire.Hosting.Azure.ApplicationInsights": "9.4.0", "Aspire.Hosting.Azure.CosmosDB": "9.4.0", "Aspire.Hosting.Azure.EventHubs": "9.4.0", "Aspire.Hosting.Azure.KeyVault": "9.4.0", "Aspire.Hosting.Azure.PostgreSQL": "9.4.0", "Aspire.Hosting.Azure.Redis": "9.4.0", "Aspire.Hosting.Azure.Search": "9.4.0", "Aspire.Hosting.Azure.ServiceBus": "9.4.0", "Aspire.Hosting.Azure.Sql": "9.4.0", "Aspire.Hosting.Azure.Storage": "9.4.0", "Aspire.Hosting.Keycloak": "9.3.1-preview.1.25305.6", "Aspire.Hosting.MongoDB": "9.4.0", "Aspire.Hosting.MySql": "9.4.0", "Aspire.Hosting.Orchestration.win-x64": "9.4.0", "Aspire.Hosting.PostgreSQL": "9.4.0", "Aspire.Hosting.RabbitMQ": "9.4.0", "Aspire.Hosting.Redis": "9.4.0", "Aspire.Hosting.SqlServer": "9.4.0", "CommunityToolkit.Aspire.Hosting.Java": "9.7.0", "CommunityToolkit.Aspire.Hosting.MongoDB.Extensions": "9.7.0", "CommunityToolkit.Aspire.Hosting.PostgreSQL.Extensions": "9.7.0", "CommunityToolkit.Aspire.Hosting.Redis.Extensions": "9.7.0", "CommunityToolkit.Aspire.Hosting.SqlServer.Extensions": "9.7.0", "Elastic.Aspire.Hosting.Elasticsearch": "9.3.0"}, "runtime": {"Miller.WMS.Edge.AppHost.dll": {}}}, "Aspire.Dashboard.Sdk.win-x64/9.4.0": {}, "Aspire.Hosting/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}, "resources": {"lib/net8.0/cs/Aspire.Hosting.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Aspire.Hosting.resources.dll": {"locale": "de"}, "lib/net8.0/es/Aspire.Hosting.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Aspire.Hosting.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Aspire.Hosting.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Aspire.Hosting.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Aspire.Hosting.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Aspire.Hosting.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Aspire.Hosting.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Aspire.Hosting.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Aspire.Hosting.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Aspire.Hosting.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Aspire.Hosting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Aspire.Hosting.AppHost/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.4.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net9.0/Aspire.Hosting.AppHost.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.AppConfiguration/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.AppConfiguration": "1.1.0", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.AppConfiguration.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.AppContainers/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Aspire.Hosting.Azure.OperationalInsights": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.AppContainers": "1.1.0", "Azure.Provisioning.ContainerRegistry": "1.1.0", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.Provisioning.OperationalInsights": "1.1.0", "Azure.Provisioning.Storage": "1.1.2", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.AppContainers.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.ApplicationInsights/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Aspire.Hosting.Azure.OperationalInsights": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.ApplicationInsights": "1.1.0", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.Provisioning.OperationalInsights": "1.1.0", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.ApplicationInsights.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.CosmosDB/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.CosmosDb": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Aspire.Hosting.Azure.KeyVault": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.CosmosDB": "1.0.0", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Azure.Cosmos": "3.52.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.CosmosDB.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.EventHubs/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Azure.Storage.Blobs": "9.0.0", "AspNetCore.HealthChecks.Azure.Storage.Queues": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Aspire.Hosting.Azure.Storage": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.EventHubs": "1.1.0", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.Provisioning.Storage": "1.1.2", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Azure.Storage.Blobs": "12.24.1", "Azure.Storage.Queues": "12.22.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.EventHubs.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.KeyVault/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.KeyVault.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.OperationalInsights/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.Provisioning.OperationalInsights": "1.1.0", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.OperationalInsights.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.PostgreSQL/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Aspire.Hosting.Azure.KeyVault": "9.4.0", "Aspire.Hosting.PostgreSQL": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.Provisioning.PostgreSql": "1.1.1", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.Redis/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Redis": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Aspire.Hosting.Azure.KeyVault": "9.4.0", "Aspire.Hosting.Redis": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.Provisioning.Redis": "1.1.0", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StackExchange.Redis": "2.8.41", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.Search/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.Provisioning.Search": "1.0.0", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.Search.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.ServiceBus/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.Provisioning.ServiceBus": "1.1.0", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.ServiceBus.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.Sql/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.SqlServer": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Aspire.Hosting.SqlServer": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.Provisioning.Sql": "1.1.0", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Data.SqlClient": "6.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net9.0/Aspire.Hosting.Azure.Sql.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Azure.Storage/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Azure.Storage.Blobs": "9.0.0", "AspNetCore.HealthChecks.Azure.Storage.Queues": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting.Azure": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Azure.Provisioning": "1.2.1", "Azure.Provisioning.KeyVault": "1.1.0", "Azure.Provisioning.Storage": "1.1.2", "Azure.ResourceManager.Authorization": "1.1.4", "Azure.ResourceManager.KeyVault": "1.3.2", "Azure.ResourceManager.Resources": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.8.0", "Azure.Storage.Blobs": "12.24.1", "Azure.Storage.Queues": "12.22.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Azure.Storage.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Keycloak/9.3.1-preview.1.25305.6": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.4.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Keycloak.dll": {"assemblyVersion": "*******", "fileVersion": "9.300.125.30506"}}}, "Aspire.Hosting.MongoDB/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.MongoDb": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.4.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "MongoDB.Driver": "3.4.0", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.MongoDB.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.MySql/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.MySql": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.4.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Orchestration.win-x64/9.4.0": {}, "Aspire.Hosting.PostgreSQL/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.4.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.RabbitMQ/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Rabbitmq": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.4.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "RabbitMQ.Client": "7.1.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.RabbitMQ.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.Redis/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.Redis": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.4.0", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StackExchange.Redis": "2.8.41", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Aspire.Hosting.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "Aspire.Hosting.SqlServer/9.4.0": {"dependencies": {"AspNetCore.HealthChecks.SqlServer": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.4.0", "Azure.Core": "1.47.0", "Azure.Identity": "1.14.2", "Google.Protobuf": "3.31.1", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "17.0.4", "Microsoft.Data.SqlClient": "6.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.6.2", "Semver": "3.0.0", "StreamJsonRpc": "2.22.11", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net9.0/Aspire.Hosting.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.37808"}}}, "AspNetCore.HealthChecks.Azure.Storage.Blobs/9.0.0": {"dependencies": {"Azure.Storage.Blobs": "12.24.1", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7"}, "runtime": {"lib/net8.0/HealthChecks.Azure.Storage.Blobs.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.Azure.Storage.Queues/9.0.0": {"dependencies": {"Azure.Storage.Queues": "12.22.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7"}, "runtime": {"lib/net8.0/HealthChecks.Azure.Storage.Queues.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.CosmosDb/9.0.0": {"dependencies": {"Microsoft.Azure.Cosmos": "3.52.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7"}, "runtime": {"lib/net8.0/HealthChecks.CosmosDb.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.MongoDb/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "MongoDB.Driver": "3.4.0"}, "runtime": {"lib/net8.0/HealthChecks.MongoDb.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.MySql/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "MySqlConnector": "2.3.1"}, "runtime": {"lib/net8.0/HealthChecks.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.NpgSql/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Npgsql": "8.0.3"}, "runtime": {"lib/net8.0/HealthChecks.NpgSql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.Rabbitmq/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "RabbitMQ.Client": "7.1.2"}, "runtime": {"lib/net8.0/HealthChecks.Rabbitmq.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.Redis/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "StackExchange.Redis": "2.8.41"}, "runtime": {"lib/net8.0/HealthChecks.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.SqlServer/9.0.0": {"dependencies": {"Microsoft.Data.SqlClient": "6.0.2", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7"}, "runtime": {"lib/net8.0/HealthChecks.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.Uris/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Http": "9.0.7"}, "runtime": {"lib/net8.0/HealthChecks.Uris.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Azure.Core/1.47.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.5.0", "System.Memory.Data": "8.0.1"}, "runtime": {"lib/net8.0/Azure.Core.dll": {"assemblyVersion": "1.47.0.0", "fileVersion": "1.4700.25.35905"}}}, "Azure.Identity/1.14.2": {"dependencies": {"Azure.Core": "1.47.0", "Microsoft.Identity.Client": "4.73.1", "Microsoft.Identity.Client.Extensions.Msal": "4.73.1", "System.Memory": "4.5.5"}, "runtime": {"lib/net8.0/Azure.Identity.dll": {"assemblyVersion": "1.14.2.0", "fileVersion": "1.1400.225.36004"}}}, "Azure.Provisioning/1.2.1": {"dependencies": {"Azure.Core": "1.47.0"}, "runtime": {"lib/net8.0/Azure.Provisioning.dll": {"assemblyVersion": "1.2.1.0", "fileVersion": "1.200.125.36003"}}}, "Azure.Provisioning.AppConfiguration/1.1.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.AppConfiguration.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.25.31702"}}}, "Azure.Provisioning.AppContainers/1.1.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.AppContainers.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.25.31702"}}}, "Azure.Provisioning.ApplicationInsights/1.1.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.ApplicationInsights.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.25.31702"}}}, "Azure.Provisioning.ContainerRegistry/1.1.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.ContainerRegistry.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.25.31702"}}}, "Azure.Provisioning.CosmosDB/1.0.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1", "System.ClientModel": "1.5.0", "System.Text.Json": "6.0.11"}, "runtime": {"lib/netstandard2.0/Azure.Provisioning.CosmosDB.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.52503"}}}, "Azure.Provisioning.EventHubs/1.1.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.EventHubs.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.25.31702"}}}, "Azure.Provisioning.KeyVault/1.1.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.KeyVault.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.25.31702"}}}, "Azure.Provisioning.OperationalInsights/1.1.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.OperationalInsights.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.25.31802"}}}, "Azure.Provisioning.PostgreSql/1.1.1": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.PostgreSql.dll": {"assemblyVersion": "1.1.1.0", "fileVersion": "1.100.125.32403"}}}, "Azure.Provisioning.Redis/1.1.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.Redis.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.25.31702"}}}, "Azure.Provisioning.Search/1.0.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1", "System.ClientModel": "1.5.0", "System.Text.Json": "6.0.11"}, "runtime": {"lib/netstandard2.0/Azure.Provisioning.Search.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.52503"}}}, "Azure.Provisioning.ServiceBus/1.1.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.ServiceBus.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.25.31702"}}}, "Azure.Provisioning.Sql/1.1.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.Sql.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.25.31702"}}}, "Azure.Provisioning.Storage/1.1.2": {"dependencies": {"Azure.Core": "1.47.0", "Azure.Provisioning": "1.2.1"}, "runtime": {"lib/net8.0/Azure.Provisioning.Storage.dll": {"assemblyVersion": "1.1.2.0", "fileVersion": "1.100.225.36501"}}}, "Azure.ResourceManager/1.13.1": {"dependencies": {"Azure.Core": "1.47.0"}, "runtime": {"lib/net8.0/Azure.ResourceManager.dll": {"assemblyVersion": "1.13.1.0", "fileVersion": "1.1300.125.22409"}}}, "Azure.ResourceManager.Authorization/1.1.4": {"dependencies": {"Azure.Core": "1.47.0", "Azure.ResourceManager": "1.13.1", "System.ClientModel": "1.5.0", "System.Text.Json": "6.0.11"}, "runtime": {"lib/net8.0/Azure.ResourceManager.Authorization.dll": {"assemblyVersion": "1.1.4.0", "fileVersion": "1.100.425.16304"}}}, "Azure.ResourceManager.KeyVault/1.3.2": {"dependencies": {"Azure.Core": "1.47.0", "Azure.ResourceManager": "1.13.1"}, "runtime": {"lib/net8.0/Azure.ResourceManager.KeyVault.dll": {"assemblyVersion": "1.3.2.0", "fileVersion": "1.300.225.20202"}}}, "Azure.ResourceManager.Resources/1.11.0": {"dependencies": {"Azure.Core": "1.47.0", "Azure.ResourceManager": "1.13.1"}, "runtime": {"lib/net8.0/Azure.ResourceManager.Resources.dll": {"assemblyVersion": "1.11.0.0", "fileVersion": "1.1100.25.32301"}}}, "Azure.Security.KeyVault.Secrets/4.8.0": {"dependencies": {"Azure.Core": "1.47.0"}, "runtime": {"lib/net8.0/Azure.Security.KeyVault.Secrets.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.800.25.31702"}}}, "Azure.Storage.Blobs/12.24.1": {"dependencies": {"Azure.Storage.Common": "12.23.0"}, "runtime": {"lib/net8.0/Azure.Storage.Blobs.dll": {"assemblyVersion": "12.24.1.0", "fileVersion": "12.2400.125.31006"}}}, "Azure.Storage.Common/12.23.0": {"dependencies": {"Azure.Core": "1.47.0", "System.IO.Hashing": "9.0.7"}, "runtime": {"lib/net8.0/Azure.Storage.Common.dll": {"assemblyVersion": "*********", "fileVersion": "12.2300.25.16105"}}}, "Azure.Storage.Queues/12.22.0": {"dependencies": {"Azure.Storage.Common": "12.23.0", "System.Memory.Data": "8.0.1"}, "runtime": {"lib/net8.0/Azure.Storage.Queues.dll": {"assemblyVersion": "1********", "fileVersion": "12.2200.25.16105"}}}, "CommunityToolkit.Aspire.Hosting.Adminer/9.7.0": {"dependencies": {"Aspire.Hosting": "9.4.0"}, "runtime": {"lib/net9.0/CommunityToolkit.Aspire.Hosting.Adminer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.Aspire.Hosting.DbGate/9.7.0": {"dependencies": {"Aspire.Hosting": "9.4.0"}, "runtime": {"lib/net9.0/CommunityToolkit.Aspire.Hosting.DbGate.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.Aspire.Hosting.Java/9.7.0": {"dependencies": {"Aspire.Hosting": "9.4.0"}, "runtime": {"lib/net9.0/CommunityToolkit.Aspire.Hosting.Java.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.Aspire.Hosting.MongoDB.Extensions/9.7.0": {"dependencies": {"Aspire.Hosting": "9.4.0", "Aspire.Hosting.MongoDB": "9.4.0", "CommunityToolkit.Aspire.Hosting.DbGate": "9.7.0"}, "runtime": {"lib/net9.0/CommunityToolkit.Aspire.Hosting.MongoDB.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.Aspire.Hosting.PostgreSQL.Extensions/9.7.0": {"dependencies": {"Aspire.Hosting": "9.4.0", "Aspire.Hosting.PostgreSQL": "9.4.0", "CommunityToolkit.Aspire.Hosting.Adminer": "9.7.0", "CommunityToolkit.Aspire.Hosting.DbGate": "9.7.0"}, "runtime": {"lib/net9.0/CommunityToolkit.Aspire.Hosting.PostgreSQL.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.Aspire.Hosting.Redis.Extensions/9.7.0": {"dependencies": {"Aspire.Hosting": "9.4.0", "Aspire.Hosting.Redis": "9.4.0", "CommunityToolkit.Aspire.Hosting.DbGate": "9.7.0"}, "runtime": {"lib/net9.0/CommunityToolkit.Aspire.Hosting.Redis.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.Aspire.Hosting.SqlServer.Extensions/9.7.0": {"dependencies": {"Aspire.Hosting": "9.4.0", "Aspire.Hosting.SqlServer": "9.4.0", "CommunityToolkit.Aspire.Hosting.Adminer": "9.7.0", "CommunityToolkit.Aspire.Hosting.DbGate": "9.7.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/CommunityToolkit.Aspire.Hosting.SqlServer.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elastic.Aspire.Hosting.Elasticsearch/9.3.0": {"dependencies": {"Aspire.Hosting": "9.4.0", "Elastic.Clients.Elasticsearch": "8.17.3", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.7", "Microsoft.Extensions.Hosting": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "MinVer": "6.0.0"}, "runtime": {"lib/net8.0/Elastic.Aspire.Hosting.Elasticsearch.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elastic.Clients.Elasticsearch/8.17.3": {"dependencies": {"Elastic.Transport": "0.5.9"}, "runtime": {"lib/net8.0/Elastic.Clients.Elasticsearch.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Elastic.Transport/0.5.9": {"runtime": {"lib/net8.0/Elastic.Transport.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "*******"}}}, "Fractions/7.3.0": {"runtime": {"lib/netstandard2.1/Fractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Google.Protobuf/3.31.1": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Grpc.AspNetCore/2.71.0": {"dependencies": {"Google.Protobuf": "3.31.1", "Grpc.AspNetCore.Server.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0"}}, "Grpc.AspNetCore.Server/2.71.0": {"dependencies": {"Grpc.Net.Common": "2.71.0"}, "runtime": {"lib/net9.0/Grpc.AspNetCore.Server.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.AspNetCore.Server.ClientFactory/2.71.0": {"dependencies": {"Grpc.AspNetCore.Server": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0"}, "runtime": {"lib/net9.0/Grpc.AspNetCore.Server.ClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.Core.Api/2.71.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.Net.Client/2.71.0": {"dependencies": {"Grpc.Net.Common": "2.71.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net8.0/Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.Net.ClientFactory/2.71.0": {"dependencies": {"Grpc.Net.Client": "2.71.0", "Microsoft.Extensions.Http": "9.0.7"}, "runtime": {"lib/net8.0/Grpc.Net.ClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.Net.Common/2.71.0": {"dependencies": {"Grpc.Core.Api": "2.71.0"}, "runtime": {"lib/net8.0/Grpc.Net.Common.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.Tools/2.72.0": {}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Json.More.Net/2.1.0": {"runtime": {"lib/net9.0/Json.More.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.0.0"}}}, "JsonPatch.Net/3.3.0": {"dependencies": {"JsonPointer.Net": "5.2.0"}, "runtime": {"lib/net9.0/JsonPatch.Net.dll": {"assemblyVersion": "*******", "fileVersion": "3.3.0.0"}}}, "JsonPointer.Net/5.2.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Json.More.Net": "2.1.0"}, "runtime": {"lib/net9.0/JsonPointer.Net.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.2.0.0"}}}, "KubernetesClient/17.0.4": {"dependencies": {"Fractions": "7.3.0", "YamlDotNet": "16.3.0"}, "runtime": {"lib/net9.0/KubernetesClient.dll": {"assemblyVersion": "1*******", "fileVersion": "17.0.4.61714"}}}, "MessagePack/2.5.192": {"dependencies": {"MessagePack.Annotations": "2.5.192", "Microsoft.NET.StringTools": "17.6.3"}, "runtime": {"lib/net6.0/MessagePack.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.192.54228"}}}, "MessagePack.Annotations/2.5.192": {"runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.192.54228"}}}, "Microsoft.Azure.Cosmos/3.52.0": {"dependencies": {"Azure.Core": "1.47.0", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Bcl.HashCode": "1.1.0", "System.Buffers": "4.5.1", "System.Collections.Immutable": "1.7.0", "System.Configuration.ConfigurationManager": "9.0.4", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Memory": "4.5.5", "System.Net.Http": "4.3.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Azure.Cosmos.Client.dll": {"assemblyVersion": "3.52.0.0", "fileVersion": "3.52.0.0"}, "lib/netstandard2.0/Microsoft.Azure.Cosmos.Core.dll": {"assemblyVersion": "2.11.0.0", "fileVersion": "2.11.0.0"}, "lib/netstandard2.0/Microsoft.Azure.Cosmos.Direct.dll": {"assemblyVersion": "3.39.1.0", "fileVersion": "3.39.1.0"}, "lib/netstandard2.0/Microsoft.Azure.Cosmos.Serialization.HybridRow.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}, "runtimeTargets": {"runtimes/win-x64/native/Cosmos.CRTCompat.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Azure.Cosmos.ServiceInterop.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.14.0.0"}, "runtimes/win-x64/native/msvcp140.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.41.34123.0"}, "runtimes/win-x64/native/vcruntime140.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.41.34123.0"}, "runtimes/win-x64/native/vcruntime140_1.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.41.34123.0"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Bcl.Cryptography/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Bcl.Cryptography.dll": {"assemblyVersion": "9.0.0.4", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Bcl.HashCode/1.1.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Microsoft.Data.SqlClient/6.0.2": {"dependencies": {"Azure.Identity": "1.14.2", "Microsoft.Bcl.Cryptography": "9.0.4", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.IdentityModel.JsonWebTokens": "7.5.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.5.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.4", "System.Security.Cryptography.Pkcs": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.2.25115.4"}}, "resources": {"lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.2.25115.4"}, "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.2.25115.4"}}}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "6.2.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.2.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "6.2.0.0"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.FileProviders.Physical": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Json/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.7", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Json": "9.0.7", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.FileProviders.Physical": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Diagnostics/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.7", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.7": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.7": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.FileSystemGlobbing": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Hosting/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.Configuration.CommandLine": "9.0.7", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.7", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.7", "Microsoft.Extensions.Configuration.Json": "9.0.7", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.7", "Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics": "9.0.7", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.FileProviders.Physical": "9.0.7", "Microsoft.Extensions.Hosting.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Configuration": "9.0.7", "Microsoft.Extensions.Logging.Console": "9.0.7", "Microsoft.Extensions.Logging.Debug": "9.0.7", "Microsoft.Extensions.Logging.EventLog": "9.0.7", "Microsoft.Extensions.Logging.EventSource": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.7", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Http/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.Console/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Configuration": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.Debug/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "System.Diagnostics.EventLog": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Options/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Primitives/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Identity.Client/4.73.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.5.0", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.73.1.0", "fileVersion": "4.73.1.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.73.1": {"dependencies": {"Microsoft.Identity.Client": "4.73.1", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.73.1.0", "fileVersion": "4.73.1.0"}}}, "Microsoft.IdentityModel.Abstractions/7.5.0": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.50326"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.5.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.50326"}}}, "Microsoft.IdentityModel.Logging/7.5.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.50326"}}}, "Microsoft.IdentityModel.Protocols/7.5.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.50326"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.5.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.5.0", "System.IdentityModel.Tokens.Jwt": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.50326"}}}, "Microsoft.IdentityModel.Tokens/7.5.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.50326"}}}, "Microsoft.NET.StringTools/17.6.3": {"runtime": {"lib/net7.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "*******", "fileVersion": "17.6.3.22601"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"dependencies": {"Microsoft.VisualStudio.Validation": "17.8.8"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Threading.dll": {"assemblyVersion": "17.13.0.0", "fileVersion": "17.13.61.36374"}}, "resources": {"lib/net8.0/cs/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Validation/17.8.8": {"runtime": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"assemblyVersion": "17.8.0.0", "fileVersion": "17.8.8.15457"}}, "resources": {"lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "MinVer/6.0.0": {}, "MongoDB.Bson/3.4.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/MongoDB.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MongoDB.Driver/3.4.0": {"dependencies": {"DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "MongoDB.Bson": "3.4.0", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "runtime": {"lib/net6.0/MongoDB.Driver.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MySqlConnector/2.3.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net8.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nerdbank.Streams/2.12.87": {"dependencies": {"Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Nerdbank.Streams.dll": {"assemblyVersion": "********", "fileVersion": "2.12.87.45167"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Npgsql/8.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"dependencies": {"System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}}, "Polly.Core/8.6.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.2.4790"}}}, "RabbitMQ.Client/7.1.2": {"dependencies": {"System.IO.Pipelines": "8.0.0", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "Semver/3.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net5.0/Semver.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Snappier/1.0.0": {"runtime": {"lib/net5.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StackExchange.Redis/2.8.41": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net8.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.8.41.44383"}}}, "StreamJsonRpc/2.22.11": {"dependencies": {"MessagePack": "2.5.192", "Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8", "Nerdbank.Streams": "2.12.87", "Newtonsoft.Json": "13.0.3", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/StreamJsonRpc.dll": {"assemblyVersion": "********", "fileVersion": "2.22.11.52697"}}, "resources": {"lib/net8.0/cs/StreamJsonRpc.resources.dll": {"locale": "cs"}, "lib/net8.0/de/StreamJsonRpc.resources.dll": {"locale": "de"}, "lib/net8.0/es/StreamJsonRpc.resources.dll": {"locale": "es"}, "lib/net8.0/fr/StreamJsonRpc.resources.dll": {"locale": "fr"}, "lib/net8.0/it/StreamJsonRpc.resources.dll": {"locale": "it"}, "lib/net8.0/ja/StreamJsonRpc.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/StreamJsonRpc.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/StreamJsonRpc.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/StreamJsonRpc.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/StreamJsonRpc.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/StreamJsonRpc.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/StreamJsonRpc.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/StreamJsonRpc.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Buffers/4.5.1": {}, "System.ClientModel/1.5.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.7", "System.Memory.Data": "8.0.1"}, "runtime": {"lib/net8.0/System.ClientModel.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.500.25.35707"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/1.7.0": {}, "System.Configuration.ConfigurationManager/9.0.4": {"dependencies": {"System.Diagnostics.EventLog": "9.0.7", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/8.0.1": {}, "System.Diagnostics.EventLog/9.0.7": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/7.5.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.5.0", "Microsoft.IdentityModel.Tokens": "7.5.0"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.50326"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Hashing/9.0.7": {"runtime": {"lib/net9.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.IO.Pipelines/8.0.0": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Memory/4.5.5": {}, "System.Memory.Data/8.0.1": {"runtime": {"lib/net8.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/9.0.4": {"runtime": {"lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/9.0.4": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Json/6.0.11": {}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.RateLimiting/8.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.ValueTuple/4.5.0": {}, "YamlDotNet/16.3.0": {"runtime": {"lib/net8.0/YamlDotNet.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "ZstdSharp.Port/0.7.3": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Miller.WMS.Edge.AppHost/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Aspire.Dashboard.Sdk.win-x64/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-RHuBBVwtu1yEDOdOFqLdeBWZ/2Ad9wBoxPEchnWK+JmkFYcbeW87J3rfXK2DgDRQqZpUq8eroIbo6w1Ez3nVtg==", "path": "aspire.dashboard.sdk.win-x64/9.4.0", "hashPath": "aspire.dashboard.sdk.win-x64.9.4.0.nupkg.sha512"}, "Aspire.Hosting/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-TQmD17D0ZgP6buXmnzJ0Xi3zF87qOiFwZZiPyvOgsdQbuNeLTukmtE3CyhRRXNWOX3+xtDas92Z7iiDB99I7IQ==", "path": "aspire.hosting/9.4.0", "hashPath": "aspire.hosting.9.4.0.nupkg.sha512"}, "Aspire.Hosting.AppHost/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-uszesnFOt7a8hny/TYQE+R377UrTyMNhDMWSSX+scDKSWGF8DAeVGLdB9If85f/FXvStMnboclE2Mxtt3P7sCQ==", "path": "aspire.hosting.apphost/9.4.0", "hashPath": "aspire.hosting.apphost.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOqvIrkw2sLBqXbxBqxVd/Qp8oNUBfjOSHX4ZKfFJKW4FVDR4emJ4Ffzl+EtBF8da4MoIB4rGPQjD3iZuSgG3Q==", "path": "aspire.hosting.azure/9.4.0", "hashPath": "aspire.hosting.azure.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.AppConfiguration/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-l1W5mW2Jf/00x/RuvVchdp5YALexuqtgqRQCU1n37Eb8/fNXIc09S189JzK+k9DSwiqcJQJgFF4YR7thdVdPjA==", "path": "aspire.hosting.azure.appconfiguration/9.4.0", "hashPath": "aspire.hosting.azure.appconfiguration.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.AppContainers/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-/CEgx6Xgt3IPHmJd9VyWlCNYo7Sysv9l/Ixv5MxX5o5SkKuwIYhe7gUJlNNY69sMBGvtTjNHozwQYWQ0+sw7Lw==", "path": "aspire.hosting.azure.appcontainers/9.4.0", "hashPath": "aspire.hosting.azure.appcontainers.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.ApplicationInsights/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-N2in5YboQ7sGQ9iY7w58QAv8U/2ttmBrtiTREHk8B/MD8ZfbpoxeUPY4z4eXzaJib0Xx45dfDs07lvarxQEN0Q==", "path": "aspire.hosting.azure.applicationinsights/9.4.0", "hashPath": "aspire.hosting.azure.applicationinsights.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.CosmosDB/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ou8U7MRjLeGOFe9Umq45MvZ/N+5dKiC4S78MiDWTjvf8aXC+k4P/lJ+j4uSSMXC5/yDCvqy0tOiEh0+0JpZeEg==", "path": "aspire.hosting.azure.cosmosdb/9.4.0", "hashPath": "aspire.hosting.azure.cosmosdb.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.EventHubs/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-/oL06p3RRQVYUbon/utxBgwLHWzI/eBAuNugxxdBkT5+d54sVY9nc0Dd2snW2xrChZAXskd+HjngiZS6oUc9VA==", "path": "aspire.hosting.azure.eventhubs/9.4.0", "hashPath": "aspire.hosting.azure.eventhubs.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.KeyVault/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-J5Hj59g8DzglHsUvGGljjxsGGXO1xiYE0FSIkLEkpUNJdW1bIefaum5Q+hXWBxVz4mXa39kZdc3nmMUN6dVMqA==", "path": "aspire.hosting.azure.keyvault/9.4.0", "hashPath": "aspire.hosting.azure.keyvault.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.OperationalInsights/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-mF9Xv8d0L1Savyt9Znur7fWjnf8+otyJrdQU8RYTaqXrynrqgpErjsR0IWfsNCwgRE0aOkrVnz2Hsjin22dqWw==", "path": "aspire.hosting.azure.operationalinsights/9.4.0", "hashPath": "aspire.hosting.azure.operationalinsights.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.PostgreSQL/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-CX/qsdDiB0jE0MMCtpIDhkMwCDpDGGDNng6Q+OiC8S8MDOm4Ac90a7HrmHTTGPpaxyRKGLoFKCidIkWMSH4hNA==", "path": "aspire.hosting.azure.postgresql/9.4.0", "hashPath": "aspire.hosting.azure.postgresql.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.Redis/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-ysgq7/aMvmh8ATCysvVepuIJVorLXmwSdYy6C5N3RhWL9TLlg6ll/8GZwFQM/Zgsuuz36HG2f6HioyyxtK9jog==", "path": "aspire.hosting.azure.redis/9.4.0", "hashPath": "aspire.hosting.azure.redis.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.Search/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-jigK9TpvpjvzFa/AkTy5lt0WPEtv1TyYdmQNuvNPTLeQ+ElqFVIw4Tj1iztorghmtWX9vpiywoPpg1XzyeQUJQ==", "path": "aspire.hosting.azure.search/9.4.0", "hashPath": "aspire.hosting.azure.search.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.ServiceBus/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-ucv7SYPswFYhwFzoTsMBxzeJ0AOqXzJjqjRJx5y2JTSTSYlTKSmX2fcV4TW/iJD45dHCuvfNj5OrOfKRkBVL3g==", "path": "aspire.hosting.azure.servicebus/9.4.0", "hashPath": "aspire.hosting.azure.servicebus.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.Sql/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Q7oVXFdg0wrefPbKO3gQUkcwJ6b7Kch9HZLEgDifzedJ0NYAQ/QhxOsPm6vFMq9w5M+RpHv49NKP6HF8YZWh0Q==", "path": "aspire.hosting.azure.sql/9.4.0", "hashPath": "aspire.hosting.azure.sql.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Azure.Storage/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-J1nt+CPzmRluidquRCG77P75D9YTKB0FpmZbm8ffyN/AyFZXuZvWZ5Sc/Vgui0dOSBZ3NRIynszqZ65wY2F6mg==", "path": "aspire.hosting.azure.storage/9.4.0", "hashPath": "aspire.hosting.azure.storage.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Keycloak/9.3.1-preview.1.25305.6": {"type": "package", "serviceable": true, "sha512": "sha512-pe8UD8VSmu4+Owz0K+<PERSON>jun+5j4eUhQWbdwfUqbHI1MyrCAf8BdObI/KxJlgmHyurvfF9OPsrxafy/o17GFsRcA==", "path": "aspire.hosting.keycloak/9.3.1-preview.1.25305.6", "hashPath": "aspire.hosting.keycloak.9.3.1-preview.1.25305.6.nupkg.sha512"}, "Aspire.Hosting.MongoDB/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Fpt7lM7+YmUAZa+SBS997/So2rYIthVSfjXnBtKGmpP/SPOjgxBhJZo3MdKqqgSlo8Qn8+6f+dBNjieFfzYI8A==", "path": "aspire.hosting.mongodb/9.4.0", "hashPath": "aspire.hosting.mongodb.9.4.0.nupkg.sha512"}, "Aspire.Hosting.MySql/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-hHfdMLdvkcwnH6AO2cLgGVyMYEGKVvu1dNk6RRJgIMdU4cGE6qQyV0MJ1BHO/Kn4Yh+YkkWEuy2f+szb3HkC2Q==", "path": "aspire.hosting.mysql/9.4.0", "hashPath": "aspire.hosting.mysql.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Orchestration.win-x64/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-thU8dQJ4n+46YnzEG6ykPHzJW3fheER0DdDgZz2NnTGZCpUdxJOAwzXsiQ4OcJEiIHJ0W5Qa4/JKyvqFFFeksg==", "path": "aspire.hosting.orchestration.win-x64/9.4.0", "hashPath": "aspire.hosting.orchestration.win-x64.9.4.0.nupkg.sha512"}, "Aspire.Hosting.PostgreSQL/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-3UxyWo2EUfpStzaiERQlPXIqzb2eFpreNRzFB1Ppo61I/GDaQ/SIcJt9/lPX7ANs3iwybgRBdqS88lp0WwaYyQ==", "path": "aspire.hosting.postgresql/9.4.0", "hashPath": "aspire.hosting.postgresql.9.4.0.nupkg.sha512"}, "Aspire.Hosting.RabbitMQ/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-iC7Z7IAvOki/8WR4xu+exKNjwL+iTCO22giitaOhbdpUU4RilpGhr7JxLIrwgidj0mJ12nACXAuzAuMHarbdsw==", "path": "aspire.hosting.rabbitmq/9.4.0", "hashPath": "aspire.hosting.rabbitmq.9.4.0.nupkg.sha512"}, "Aspire.Hosting.Redis/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-hRpzHpGrvzCcy3dN80JJdL7yt82OvQUTCPWlUTDsIxbPoqxeDTKoHO+Z5d1dl8ZBPqhfz30hIDzVhIvZFyeT+w==", "path": "aspire.hosting.redis/9.4.0", "hashPath": "aspire.hosting.redis.9.4.0.nupkg.sha512"}, "Aspire.Hosting.SqlServer/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-NlVLcud+74IByLOVaj03ls/OImWQU5inlXAvI1OB9ts9yB9OhVbxbDrbj5/uWOW2FDnZxtZCuD/Q23a9NCoXzA==", "path": "aspire.hosting.sqlserver/9.4.0", "hashPath": "aspire.hosting.sqlserver.9.4.0.nupkg.sha512"}, "AspNetCore.HealthChecks.Azure.Storage.Blobs/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fORaHfhyerL+7Tu4lTyj5CBHEMPYdH0LS1bm9N2Wd/K4mgd/zzg+iXLFBYTNTbF+hLYpVNZyO2Tx/wU0/s4KqQ==", "path": "aspnetcore.healthchecks.azure.storage.blobs/9.0.0", "hashPath": "aspnetcore.healthchecks.azure.storage.blobs.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.Azure.Storage.Queues/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ne+XNpiO2yVH9g6x5B1SJBMzNz8JR0YRiBvJR9qGdpTsHtcTY8GhmA9+mznQ+nTLgUQNvEbr6Xj8Ha9zjW0QDQ==", "path": "aspnetcore.healthchecks.azure.storage.queues/9.0.0", "hashPath": "aspnetcore.healthchecks.azure.storage.queues.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.CosmosDb/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lb1dZiZE88ipN2Ied4XsgZYorEIOvXoX2cHI4K9JhzGCAe9Av28RaXuJoqYT/7GMSiCX1lzIZpYMoSp4r/xAOQ==", "path": "aspnetcore.healthchecks.cosmosdb/9.0.0", "hashPath": "aspnetcore.healthchecks.cosmosdb.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.MongoDb/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WAmROqEXlVzjgCdEa9Zor2l6T29jsnKMJQPGbTnkrgpyqPcjwOomuh91CiAebmeWJ/CKenVX+fV/VFffnskgvw==", "path": "aspnetcore.healthchecks.mongodb/9.0.0", "hashPath": "aspnetcore.healthchecks.mongodb.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.MySql/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v9UqyTFVO79tWVM2uRuUWgauZAAHdLxEqa1mujnztBbG5MzIy78ciN6fICfFAZPNJQydYmkZHqPx7/eIuJog9A==", "path": "aspnetcore.healthchecks.mysql/9.0.0", "hashPath": "aspnetcore.healthchecks.mysql.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.NpgSql/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-npc58/AD5zuVxERdhCl2Kb7WnL37mwX42SJcXIwvmEig0/dugOLg3SIwtfvvh3TnvTwR/sk5LYNkkPaBdks61A==", "path": "aspnetcore.healthchecks.npgsql/9.0.0", "hashPath": "aspnetcore.healthchecks.npgsql.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.Rabbitmq/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7WSQ7EwioA5niakzzLtGVcZMEOh+42fSwrI24vnNsT7gZuVGOViNekyz38G6wBPYKcpL/lUkMdg3ZaCiZTi/Dw==", "path": "aspnetcore.healthchecks.rabbitmq/9.0.0", "hashPath": "aspnetcore.healthchecks.rabbitmq.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.Redis/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yNH0h8GLRbAf+PU5HNVLZ5hNeyq9mDVmRKO9xuZsme/znUYoBJlQvI0gq45gaZNlLncCHkMhR4o90MuT+gxxPw==", "path": "aspnetcore.healthchecks.redis/9.0.0", "hashPath": "aspnetcore.healthchecks.redis.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.SqlServer/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UxCf65iCF2nU1u7AcB320abjL4CRg5swCgJECY6mKk1j5lrGMfVtskWwriGs1T29pYdRig9Vra3SPnP+4G82pA==", "path": "aspnetcore.healthchecks.sqlserver/9.0.0", "hashPath": "aspnetcore.healthchecks.sqlserver.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.Uris/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYdNlA437KeF8p9qOpZFyNqAN+c0FXt/JjTvzH/Qans0q0O3pPE8KPnn39ucQQjR/Roum1vLTP3kXiUs8VHyuA==", "path": "aspnetcore.healthchecks.uris/9.0.0", "hashPath": "aspnetcore.healthchecks.uris.9.0.0.nupkg.sha512"}, "Azure.Core/1.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-Da6VYrs9qNylfNSURJrytAk4EzBlBv1U62JVww+62CeD+qscD0iK0j5SM1PXhxXylAhH5kx7BuR0yrtmzFmBvA==", "path": "azure.core/1.47.0", "hashPath": "azure.core.1.47.0.nupkg.sha512"}, "Azure.Identity/1.14.2": {"type": "package", "serviceable": true, "sha512": "sha512-YhNMwOTwT+I2wIcJKSdP0ADyB2aK+JaYWZxO8LSRDm5w77LFr0ykR9xmt2ZV5T1gaI7xU6iNFIh/yW1dAlpddQ==", "path": "azure.identity/1.14.2", "hashPath": "azure.identity.1.14.2.nupkg.sha512"}, "Azure.Provisioning/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-h+pwW/FIbF3ijAIg6w5BrZxh/buWK4gCow9pZKMcRul8He5l1pELKFUZjso7ejEaftwEnHN91UClds2Gxi1h9g==", "path": "azure.provisioning/1.2.1", "hashPath": "azure.provisioning.1.2.1.nupkg.sha512"}, "Azure.Provisioning.AppConfiguration/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-2e2WbTRvZ1RuPbJ0YS1bqWAghqn7uz81Fvlw8+Bhxm0r64hTreX2zYGdLUtydzjlwoSOeAGMw0mfgaFCwvsreg==", "path": "azure.provisioning.appconfiguration/1.1.0", "hashPath": "azure.provisioning.appconfiguration.1.1.0.nupkg.sha512"}, "Azure.Provisioning.AppContainers/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-yLB+IDJ4zGyOyFkc+qVgsKsGjemGYygtp2z2JcnJG7GifQCVw/wSuQGy7sDW5gzOg9WqhkEgZSCxCohRK6j1Lw==", "path": "azure.provisioning.appcontainers/1.1.0", "hashPath": "azure.provisioning.appcontainers.1.1.0.nupkg.sha512"}, "Azure.Provisioning.ApplicationInsights/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-JFSA+i6PeQ42AO3kojRetd72gQMR50dGzHVsgK86VRROy+2wobZ9t2wgww0d9f0LAif9SwxEH4NiBloxP6ZVcQ==", "path": "azure.provisioning.applicationinsights/1.1.0", "hashPath": "azure.provisioning.applicationinsights.1.1.0.nupkg.sha512"}, "Azure.Provisioning.ContainerRegistry/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NgpzBt8Ly7r/8v4ndk7r4KBaa5N6fs+qpAHRLikigx65EDpT5f0lb1GoI+6MsygOCehOwweq98yTcYmSPG6OOg==", "path": "azure.provisioning.containerregistry/1.1.0", "hashPath": "azure.provisioning.containerregistry.1.1.0.nupkg.sha512"}, "Azure.Provisioning.CosmosDB/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y9/AzZm5TPN5XUhRheswaZETGVqNdghScLF7XXKNeCFdjPZd4RlPT83P2IYcl/IhKkT32dtN9EoaS8Y6CKY2Zw==", "path": "azure.provisioning.cosmosdb/1.0.0", "hashPath": "azure.provisioning.cosmosdb.1.0.0.nupkg.sha512"}, "Azure.Provisioning.EventHubs/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-iI/E3IuvcHaD8w61hwlvNEEBMU6wqWwOwJhVX2EOHHpqRty/oXTliWDVjsAbO78co2O7ySYCRsnAw8kL+j3ayw==", "path": "azure.provisioning.eventhubs/1.1.0", "hashPath": "azure.provisioning.eventhubs.1.1.0.nupkg.sha512"}, "Azure.Provisioning.KeyVault/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-n8SCJONrEU3w9lq/ZiQkxZ0L+Sv6RL2M95LSVYnwlYe84ww6jpo/djlfd835dh5cQvVrHgETi3UnRUcZn89J0w==", "path": "azure.provisioning.keyvault/1.1.0", "hashPath": "azure.provisioning.keyvault.1.1.0.nupkg.sha512"}, "Azure.Provisioning.OperationalInsights/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-2VzPvdGmVjHrRPKD2wrLlZVb2mJRiQqMV2kkTJPrDiZ/ijKr7VNOihhIvGJ6C6NWW398i49Y2wR05vDl3Mvt6w==", "path": "azure.provisioning.operationalinsights/1.1.0", "hashPath": "azure.provisioning.operationalinsights.1.1.0.nupkg.sha512"}, "Azure.Provisioning.PostgreSql/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-nAcxs1iRtbZHSwxKliHQz7GCQiNDhkUvL7tzLiKc7R2Kp7sO1k5Q6k8B77Ka2ul7eMHSmp5wtSmZiq8t2gqg5A==", "path": "azure.provisioning.postgresql/1.1.1", "hashPath": "azure.provisioning.postgresql.1.1.1.nupkg.sha512"}, "Azure.Provisioning.Redis/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kkafeDrP4dGMqAiHXnun4Tge2diYEhRG03s4ms2jGgtLyks+AVyj+W/GebPDg58dSKaM4egGvtacGRZTpjkSpw==", "path": "azure.provisioning.redis/1.1.0", "hashPath": "azure.provisioning.redis.1.1.0.nupkg.sha512"}, "Azure.Provisioning.Search/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LNJq9kJ9+jIGRqAba97O13jMXcwvVp7ncDoetIzczxLZa9lzdyvvh2TeCl6W+YR04edmJVzgITIHfRM+BCJoyQ==", "path": "azure.provisioning.search/1.0.0", "hashPath": "azure.provisioning.search.1.0.0.nupkg.sha512"}, "Azure.Provisioning.ServiceBus/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qv50/3+VcQI9cCwOATsLyRAiDAsZAUn6j9LMckb9kKEWJ0Cd/l73sSheEJz70MJmrHYOL/gkfwS7IY8n3PuEgQ==", "path": "azure.provisioning.servicebus/1.1.0", "hashPath": "azure.provisioning.servicebus.1.1.0.nupkg.sha512"}, "Azure.Provisioning.Sql/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-AJmH8vpTtxXM6L+McCqEB90IUqpCieV+h+TKz/mG58cJ/hNsp31hOVYaIOqOuHY5HaU9x5Y2fJRKibyleSLklg==", "path": "azure.provisioning.sql/1.1.0", "hashPath": "azure.provisioning.sql.1.1.0.nupkg.sha512"}, "Azure.Provisioning.Storage/1.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-LiTtCCxVQrXPrvbZczd+Efo9qhPlTkL/xdvz+U2HNRU9EmEy4DryomInNMvqdLa0RgHHAybqckrJAr4rjclnRg==", "path": "azure.provisioning.storage/1.1.2", "hashPath": "azure.provisioning.storage.1.1.2.nupkg.sha512"}, "Azure.ResourceManager/1.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-kpsl5R6HkBS6jpxKoqeM8y5JjjYxv0bmVOHijjO098vpyK20nMYZdceFO4/4wHEMDybAjm4HGu1uxhjD/c5Rhw==", "path": "azure.resourcemanager/1.13.1", "hashPath": "azure.resourcemanager.1.13.1.nupkg.sha512"}, "Azure.ResourceManager.Authorization/1.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-AFUFFdKJ/tYuXp+kXIqyD6jaUy22h0g8GWAkZa4e3/X+hnXndj6hxqIrlt/p+jflHB8SFPnBkxSN5QFadP4bRw==", "path": "azure.resourcemanager.authorization/1.1.4", "hashPath": "azure.resourcemanager.authorization.1.1.4.nupkg.sha512"}, "Azure.ResourceManager.KeyVault/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-hA1jiJXyUmyiUjM1RJ6D8e2mL2K2+mHjizuIw1l6K7ujueoYa49AYNoGUAVK4SSKM5OIxTGQWz2TtYaR4PJl3A==", "path": "azure.resourcemanager.keyvault/1.3.2", "hashPath": "azure.resourcemanager.keyvault.1.3.2.nupkg.sha512"}, "Azure.ResourceManager.Resources/1.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYA83A/88/bnSt7HWMECJU03hPgcQvIMWu5wyMUIPWk8TjEHvNaKNATeOI2EJ0xyQmbNiJ7Nzjfat37PNw2GLg==", "path": "azure.resourcemanager.resources/1.11.0", "hashPath": "azure.resourcemanager.resources.1.11.0.nupkg.sha512"}, "Azure.Security.KeyVault.Secrets/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-tmcIgo+de2K5+PTBRNlnFLQFbmSoyuT9RpDr5MwKS6mIfNxLPQpARkRAP91r3tmeiJ9j/UCO0F+hTlk1Bk7HNQ==", "path": "azure.security.keyvault.secrets/4.8.0", "hashPath": "azure.security.keyvault.secrets.4.8.0.nupkg.sha512"}, "Azure.Storage.Blobs/12.24.1": {"type": "package", "serviceable": true, "sha512": "sha512-479Z9ps9yl9XyhU45bbU2CU4e2B23S6FJiSiL9LpfZHU6eNXXD9Jb6rYdwY+qqmm852RhqICXBpX3Sql4DLBew==", "path": "azure.storage.blobs/12.24.1", "hashPath": "azure.storage.blobs.12.24.1.nupkg.sha512"}, "Azure.Storage.Common/12.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-X/pe1LS3lC6s6MSL7A6FzRfnB6P72rNBt5oSuyan6Q4Jxr+KiN9Ufwqo32YLHOVfPcB8ESZZ4rBDketn+J37Rw==", "path": "azure.storage.common/12.23.0", "hashPath": "azure.storage.common.12.23.0.nupkg.sha512"}, "Azure.Storage.Queues/12.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-HPQgOlfH+rJ4CL4V8ePFnsT/KKnvLU35ytxC3fsTTqOazhQ0593C0aPVu258DRN8bQCbx4OpNpjtiO9czDy3VQ==", "path": "azure.storage.queues/12.22.0", "hashPath": "azure.storage.queues.12.22.0.nupkg.sha512"}, "CommunityToolkit.Aspire.Hosting.Adminer/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-1aCd1Ec9hDY5iePRHiUVBPfOeyjsjqkKVp1ARUk7+QJUl4s05OIdEQprdVdWEntF9tFjgotrXGa5SEQgLu93jQ==", "path": "communitytoolkit.aspire.hosting.adminer/9.7.0", "hashPath": "communitytoolkit.aspire.hosting.adminer.9.7.0.nupkg.sha512"}, "CommunityToolkit.Aspire.Hosting.DbGate/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-fwlcsP9uI3vyRbxLSJOtDaSuYXfoi5pmTRztj1HecoEbUI6sFIBDBnudtiPJ6+wP8B82dxrNsGZNL2IsbrEupg==", "path": "communitytoolkit.aspire.hosting.dbgate/9.7.0", "hashPath": "communitytoolkit.aspire.hosting.dbgate.9.7.0.nupkg.sha512"}, "CommunityToolkit.Aspire.Hosting.Java/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dsu66l2HZRW1IEF6Sevdb+62BxkUtQcuZooBRbR7k39e5Q/EuULn02whNBWIekb5DlK+qX4qBWqcQiTNe+BULA==", "path": "communitytoolkit.aspire.hosting.java/9.7.0", "hashPath": "communitytoolkit.aspire.hosting.java.9.7.0.nupkg.sha512"}, "CommunityToolkit.Aspire.Hosting.MongoDB.Extensions/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CnHNQRRfj2EVRE4sy/NHnMxMPXglJbYloXCb8qjvNqORUiHMo7yODcEQD5cXJWCEbM2pM0cqGjjgbMsJHdS7cg==", "path": "communitytoolkit.aspire.hosting.mongodb.extensions/9.7.0", "hashPath": "communitytoolkit.aspire.hosting.mongodb.extensions.9.7.0.nupkg.sha512"}, "CommunityToolkit.Aspire.Hosting.PostgreSQL.Extensions/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZpDUgoxRa1i4xPFjifnDHWG7vsXrxbj16hLikt7Kh56b1fhhwevwigXL/50/TaAdxJbfL+IIDH5jRBCAVwVRug==", "path": "communitytoolkit.aspire.hosting.postgresql.extensions/9.7.0", "hashPath": "communitytoolkit.aspire.hosting.postgresql.extensions.9.7.0.nupkg.sha512"}, "CommunityToolkit.Aspire.Hosting.Redis.Extensions/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-akTgcuijiDPMc8vME1Q6586GF1EDTqjfUlW+OP7JC/0GKleI12AKl8luy2GrssX+Te2wRBjrc108ZaRVNhNEcw==", "path": "communitytoolkit.aspire.hosting.redis.extensions/9.7.0", "hashPath": "communitytoolkit.aspire.hosting.redis.extensions.9.7.0.nupkg.sha512"}, "CommunityToolkit.Aspire.Hosting.SqlServer.Extensions/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-b1Owcrr5GI75Hr5AZjAQeZtKhsnaltCn3GHGylZMnEwBmu+K2U6xOPSrJQKNWESf44639y0Uz0JhHejNeGww8Q==", "path": "communitytoolkit.aspire.hosting.sqlserver.extensions/9.7.0", "hashPath": "communitytoolkit.aspire.hosting.sqlserver.extensions.9.7.0.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "Elastic.Aspire.Hosting.Elasticsearch/9.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMo73WV/BxBhoejlgGe0RtwT4OkxHurgZ84VaXB1fzfevob3XZYNtrErw9xZQcK3o2OBwQVmSU7MYaTgeuy7rw==", "path": "elastic.aspire.hosting.elasticsearch/9.3.0", "hashPath": "elastic.aspire.hosting.elasticsearch.9.3.0.nupkg.sha512"}, "Elastic.Clients.Elasticsearch/8.17.3": {"type": "package", "serviceable": true, "sha512": "sha512-ucDLtAb6LI9ZqkS4yHkKifnDokAgNj4V3ZCahV9sMScDqDfxDeJeUMTBSm0yVExWszWsnCzhwp0oy5xM+BKx9w==", "path": "elastic.clients.elasticsearch/8.17.3", "hashPath": "elastic.clients.elasticsearch.8.17.3.nupkg.sha512"}, "Elastic.Transport/0.5.9": {"type": "package", "serviceable": true, "sha512": "sha512-RXoJAeXKaXq67SeLhyDMBPpvlkG940jqJWd5l+rKglMRLJn6X0dkLNlWehpDuouicNdwnYdcuADnqdSOiSvASw==", "path": "elastic.transport/0.5.9", "hashPath": "elastic.transport.0.5.9.nupkg.sha512"}, "Fractions/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2bETFWLBc8b7Ut2SVi+bxhGVwiSpknHYGBh2PADyGWONLkTxT7bKyDRhF8ao+XUv90tq8Fl7GTPxSI5bacIRJw==", "path": "fractions/7.3.0", "hashPath": "fractions.7.3.0.nupkg.sha512"}, "Google.Protobuf/3.31.1": {"type": "package", "serviceable": true, "sha512": "sha512-gSnJbUmGiOTdWddPhqzrEscHq9Ls6sqRDPB9WptckyjTUyx70JOOAaDLkFff8gManZNN3hllQ4aQInnQyq/Z/A==", "path": "google.protobuf/3.31.1", "hashPath": "google.protobuf.3.31.1.nupkg.sha512"}, "Grpc.AspNetCore/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-B4wAbNtAuHNiHAMxLFWL74wUElzNOOboFnypalqpX76piCOGz/w5FpilbVVYGboI4Qgl4ZmZsvDZ1zLwHNsjnw==", "path": "grpc.aspnetcore/2.71.0", "hashPath": "grpc.aspnetcore.2.71.0.nupkg.sha512"}, "Grpc.AspNetCore.Server/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-kv+9YVB6MqDYWIcstXvWrT7Xc1si/sfINzzSxvQfjC3aei+92gXDUXCH/Q+TEvi4QSICRqu92BYcrXUBW7cuOw==", "path": "grpc.aspnetcore.server/2.71.0", "hashPath": "grpc.aspnetcore.server.2.71.0.nupkg.sha512"}, "Grpc.AspNetCore.Server.ClientFactory/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-AHvMxoC+esO1e/nOYBjxvn0WDHAfglcVBjtkBy6ohgnV+PzkF8UdkPHE02xnyPFaSokWGZKnWzjgd00x6EZpyQ==", "path": "grpc.aspnetcore.server.clientfactory/2.71.0", "hashPath": "grpc.aspnetcore.server.clientfactory.2.71.0.nupkg.sha512"}, "Grpc.Core.Api/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-QquqUC37yxsDzd1QaDRsH2+uuznWPTS8CVE2Yzwl3CvU4geTNkolQXoVN812M2IwT6zpv3jsZRc9ExJFNFslTg==", "path": "grpc.core.api/2.71.0", "hashPath": "grpc.core.api.2.71.0.nupkg.sha512"}, "Grpc.Net.Client/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-U1vr20r5ngoT9nlb7wejF28EKN+taMhJsV9XtK9MkiepTZwnKxxiarriiMfCHuDAfPUm9XUjFMn/RIuJ4YY61w==", "path": "grpc.net.client/2.71.0", "hashPath": "grpc.net.client.2.71.0.nupkg.sha512"}, "Grpc.Net.ClientFactory/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-8oPLwQLPo86fmcf9ghjCDyNsSWhtHc3CXa/AqwF8Su/pG7qAoeWWtbymsZhoNvCV9Zjzb6BDcIPKXLYt+O175g==", "path": "grpc.net.clientfactory/2.71.0", "hashPath": "grpc.net.clientfactory.2.71.0.nupkg.sha512"}, "Grpc.Net.Common/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-v0c8R97TwRYwNXlC8GyRXwYTCNufpDfUtj9la+wUrZFzVWkFJuNAltU+c0yI3zu0jl54k7en6u2WKgZgd57r2Q==", "path": "grpc.net.common/2.71.0", "hashPath": "grpc.net.common.2.71.0.nupkg.sha512"}, "Grpc.Tools/2.72.0": {"type": "package", "serviceable": true, "sha512": "sha512-BCiuQ03EYjLHCo9hqZmY5barsz5vvcz/+/ICt5wCbukaePHZmMPDGelKlkxWx3q+f5xOMNHa9zXQ2N6rQZ4B+w==", "path": "grpc.tools/2.72.0", "hashPath": "grpc.tools.2.72.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Json.More.Net/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-qtwsyAsL55y2vB2/sK4Pjg3ZyVzD5KKSpV3lOAMHlnjFfsjQ/86eHJfQT9aV1YysVXzF4+xyHOZbh7Iu3YQ7Lg==", "path": "json.more.net/2.1.0", "hashPath": "json.more.net.2.1.0.nupkg.sha512"}, "JsonPatch.Net/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GIcMMDtzfzVfIpQgey8w7dhzcw6jG5nD4DDAdQCTmHfblkCvN7mI8K03to8YyUhKMl4PTR6D6nLSvWmyOGFNTg==", "path": "jsonpatch.net/3.3.0", "hashPath": "jsonpatch.net.3.3.0.nupkg.sha512"}, "JsonPointer.Net/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qe1F7Tr/p4mgwLPU9P60MbYkp+xnL2uCPnWXGgzfR/AZCunAZIC0RZ32dLGJJEhSuLEfm0YF/1R3u5C7mEVq+w==", "path": "jsonpointer.net/5.2.0", "hashPath": "jsonpointer.net.5.2.0.nupkg.sha512"}, "KubernetesClient/17.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cpWJdHP2HM2/p+GCeZf5vdIZrmGcRmfJeCJYg7YCbyYrWvIPFdR0daFdBj1k1DDZ9hIgqf6h4EvXF8pz49lDAA==", "path": "kubernetesclient/17.0.4", "hashPath": "kubernetesclient.17.0.4.nupkg.sha512"}, "MessagePack/2.5.192": {"type": "package", "serviceable": true, "sha512": "sha512-Jtle5MaFeIFkdXtxQeL9Tu2Y3HsAQGoSntOzrn6Br/jrl6c8QmG22GEioT5HBtZJR0zw0s46OnKU8ei2M3QifA==", "path": "messagepack/2.5.192", "hashPath": "messagepack.2.5.192.nupkg.sha512"}, "MessagePack.Annotations/2.5.192": {"type": "package", "serviceable": true, "sha512": "sha512-ja<PERSON><PERSON>w<PERSON>govWIZ8Zysdyf3b7b34/BrADw4v82GaEZymUhDd3ScMPrYd/cttekeDteJJPXseJxp04yTIcxiVUjTWg==", "path": "messagepack.annotations/2.5.192", "hashPath": "messagepack.annotations.2.5.192.nupkg.sha512"}, "Microsoft.Azure.Cosmos/3.52.0": {"type": "package", "serviceable": true, "sha512": "sha512-NEjNpaO19gvJrXowqHFcYPSpro5+TNjHO/JpU4VXP37by2aU2RVnmgpZsWL1GUl5wCPZ4VIOUsP1lrHpfW8ADQ==", "path": "microsoft.azure.cosmos/3.52.0", "hashPath": "microsoft.azure.cosmos.3.52.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.Cryptography/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-YgZYAWzyNuPVtPq6WNm0bqOWNjYaWgl5mBWTGZyNoXitYBUYSp6iUB9AwK0V1mo793qRJUXz2t6UZrWITZSvuQ==", "path": "microsoft.bcl.cryptography/9.0.4", "hashPath": "microsoft.bcl.cryptography.9.0.4.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-J2G1k+u5unBV+aYcwxo94ip16Rkp65pgWFb0R6zwJipzWNMgvqlWeuI7/+R+e8bob66LnSG+llLJ+z8wI94cHg==", "path": "microsoft.bcl.hashcode/1.1.0", "hashPath": "microsoft.bcl.hashcode.1.1.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-RDqwzNu5slSqGy0eSgnN4fuLdGI1w9ZHBRNALrbUsykOIbXtGCpyotG0r5zz+HHtzxbe6LtcAyWcOiu0a+Fx/A==", "path": "microsoft.data.sqlclient/6.0.2", "hashPath": "microsoft.data.sqlclient.6.0.2.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w==", "path": "microsoft.data.sqlclient.sni.runtime/6.0.2", "hashPath": "microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-30necCQehcg9lFkMEIE7HczcoYGML8GUH6jlincA18d896fLZM9wl5tpTPJHgzANQE/6KXRLZSWbgevgg5csSw==", "path": "microsoft.extensions.caching.abstractions/9.0.7", "hashPath": "microsoft.extensions.caching.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-nDu6c8fwrHQYccLnWnvyElrdkL3rZ97TZNqL+niMFUcApVBHdpDmKcRvciGymJ4Y0iLDTOo5J2XhDQEbNb+dFg==", "path": "microsoft.extensions.caching.memory/9.0.7", "hashPath": "microsoft.extensions.caching.memory.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-oxGR51+w5cXm5B9gU6XwpAB2sTiyPSmZm7hjvv0rzRnmL5o/KZzE103AuQj7sK26OBupjVzU/bZxDWvvU4nhEg==", "path": "microsoft.extensions.configuration/9.0.7", "hashPath": "microsoft.extensions.configuration.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-lut/kiVvNsQ120VERMUYSFhpXPpKjjql+giy03LesASPBBcC0o6+aoFdzJH9GaYpFTQ3fGVhVjKjvJDoAW5/IQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.7", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ExY+zXHhU4o9KC2alp3ZdLWyVWVRSn5INqax5ABk+HEOHlAHzomhJ7ek9HHliyOMiVGoYWYaMFOGr9q59mSAGA==", "path": "microsoft.extensions.configuration.binder/9.0.7", "hashPath": "microsoft.extensions.configuration.binder.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-LqwdkMNFeRyuqExewBSaWj8roEgZH8JQ9zEAmHl5ZFcnhCvjAdHICdYVRIiSEq9RWGB731LL8kZJM8tdTKEscA==", "path": "microsoft.extensions.configuration.commandline/9.0.7", "hashPath": "microsoft.extensions.configuration.commandline.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-R8kgazVpDr4k1K7MeWPLAwsi5VpwrhE3ubXK38D9gpHEvf9XhZhJ8kWHKK00LDg5hJ7pMQLggdZ7XFdQ5182Ug==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.7", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-3LVg32iMfR9ENeegXAo73L+877iOcQauLJsXlKZNVSsLA/HbPgClZdeMGdjLSkaidYw3l02XbXTlOdGYNgu91Q==", "path": "microsoft.extensions.configuration.fileextensions/9.0.7", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-3HQV326liEInT9UKEc+k73f1ECwNhvDS/DJAe5WvtMKDJTJqTH2ujrUC2ZlK/j6pXyPbV9f0Ku8JB20JveGImg==", "path": "microsoft.extensions.configuration.json/9.0.7", "hashPath": "microsoft.extensions.configuration.json.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ouDuPgRdeF4TJXKUh+lbm6QwyWwnCy+ijiqfFM2cI5NmW83MwKg1WNp2nCdMVcwQW8wJXteF/L9lA6ZPS3bCIQ==", "path": "microsoft.extensions.configuration.usersecrets/9.0.7", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-i05AYA91vgq0as84ROVCyltD2gnxaba/f1Qw2rG7mUsS0gv8cPTr1Gm7jPQHq7JTr4MJoQUcanLVs16tIOUJaQ==", "path": "microsoft.extensions.dependencyinjection/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-iPK1FxbGFr2Xb+4Y+dTYI8Gupu9pOi8I3JPuPsrogUmEhe2hzZ9LpCmolMEBhVDo2ikcSr7G5zYiwaapHSQTew==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-6ykfInm6yw7pPHJACgnrPUXxUWVslFnzad44K/siXk6Ovan6fNMnXxI5X9vphHJuZ4JbMOdPIgsfTmLD+Dyxug==", "path": "microsoft.extensions.diagnostics/9.0.7", "hashPath": "microsoft.extensions.diagnostics.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-d39Ov1JpeWCGLCOTinlaDkujhrSAQ0HFxb7Su1BjhCKBfmDcQ6Ia1i3JI6kd3NFgwi1dexTunu82daDNwt7E6w==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.7", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-nWutKqF6Eo0x8JE7Zo6eWtjjGynqjHvEQgpRTzavkU9VKwFECcZFBghpkEUCoDjeMCVafzGQTlQw7DsWHNnlsQ==", "path": "microsoft.extensions.diagnostics.healthchecks/9.0.7", "hashPath": "microsoft.extensions.diagnostics.healthchecks.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-kDvMq6n3hzQw2mvFwmj/z4OGlTPCg0fGjbM8C+4qS+SxYv4+QTFOY7nZNGfY0CGb40oPKlZQZD9TVQjMSj2atw==", "path": "microsoft.extensions.diagnostics.healthchecks.abstractions/9.0.7", "hashPath": "microsoft.extensions.diagnostics.healthchecks.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-y9djCca1cz/oz/J8jTxtoecNiNvaiGBJeWd7XOPxonH+FnfHqcfslJMcSr5JMinmWFyS7eh3C9L6m6oURZ5lSA==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.7", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-JYEPYrb+YBpFTCdmSBrk8cg3wAi1V4so7ccq04qbhg3FQHQqgJk28L3heEOKMXcZobOBUjTnGCFJD49Ez9kG5w==", "path": "microsoft.extensions.fileproviders.physical/9.0.7", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-5VKpTH2ME0SSs0lrtkpKgjCeHzXR5ka/H+qThPwuWi78wHubApZ/atD7w69FDt0OOM7UMV6LIbkqEQgoby4IXA==", "path": "microsoft.extensions.filesystemglobbing/9.0.7", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-Dkv55VfitwJjPUk9mFHxT9MJAd8su7eJNaCHhBU/Y9xFqw3ZNHwrpeptXeaXiaPtfQq+alMmawIz1Impk5pHkQ==", "path": "microsoft.extensions.hosting/9.0.7", "hashPath": "microsoft.extensions.hosting.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-yG2JCXAR+VqI1mKqynLPNJlNlrUJeEISEpX4UznOp2uM4IEFz3pDDauzyMvTjICutEJtOigJ1yWBvxbaIlibBw==", "path": "microsoft.extensions.hosting.abstractions/9.0.7", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-KV2DyFzTyZlrmKBF7IHrg+OhdetkeeByC35vVp50CZogNCbO6c4nzBcjJNnGU0S+CMcrvsN2s8OI5lHwL0wv8A==", "path": "microsoft.extensions.http/9.0.7", "hashPath": "microsoft.extensions.http.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-fdIeQpXYV8yxSWG03cCbU2Otdrq4NWuhnQLXokWLv3L9YcK055E7u8WFJvP+uuP4CFeCEoqZQL4yPcjuXhCZrg==", "path": "microsoft.extensions.logging/9.0.7", "hashPath": "microsoft.extensions.logging.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-sMM6NEAdUTE/elJ2wqjOi0iBWqZmSyaTByLF9e8XHv6DRJFFnOe0N+s8Uc6C91E4SboQCfLswaBIZ+9ZXA98AA==", "path": "microsoft.extensions.logging.abstractions/9.0.7", "hashPath": "microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-AEBty9rvFGvdFRqgIDEhQmiCnIfQWyzVoOZrO244cfu+n9M+wI1QLDpuROVILlplIBtLVmOezAF7d1H3Qog6Xw==", "path": "microsoft.extensions.logging.configuration/9.0.7", "hashPath": "microsoft.extensions.logging.configuration.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-pEHlNa8iCfKsBFA3YVDn/8EicjSU/m8uDfyoR0i4svONDss4Yu9Kznw53E/TyI+TveTo7CwRid4kfd4pLYXBig==", "path": "microsoft.extensions.logging.console/9.0.7", "hashPath": "microsoft.extensions.logging.console.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-MxzZj7XbsYJwfjclVTjJym2/nVIkksu7l7tC/4HYy+YRdDmpE4B+hTzCXu3BNfLNhdLPZsWpyXuYe6UGgWDm3g==", "path": "microsoft.extensions.logging.debug/9.0.7", "hashPath": "microsoft.extensions.logging.debug.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-usrMVsY7c8M8fESt34Y3eEIQIlRlKXfPDlI+vYEb6xT7SUjhua2ey3NpHgQktiTgz8Uo5RiWqGD8ieiyo2WaDA==", "path": "microsoft.extensions.logging.eventlog/9.0.7", "hashPath": "microsoft.extensions.logging.eventlog.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-/wwi6ckTEegCExFV6gVToCO7CvysZnmE50fpdkYUsSMh0ue9vRkQ7uOqkHyHol93ASYTEahrp+guMtS/+fZKaA==", "path": "microsoft.extensions.logging.eventsource/9.0.7", "hashPath": "microsoft.extensions.logging.eventsource.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-trJnF6cRWgR5uMmHpGoHmM1wOVFdIYlELlkO9zX+RfieK0321Y55zrcs4AaEymKup7dxgEN/uJU25CAcMNQRXw==", "path": "microsoft.extensions.options/9.0.7", "hashPath": "microsoft.extensions.options.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-pE/jeAWHEIy/8HsqYA+I1+toTsdvsv+WywAcRoNSvPoFwjOREa8Fqn7D0/i0PbiXsDLFupltTTctliePx8ib4w==", "path": "microsoft.extensions.options.configurationextensions/9.0.7", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ti/zD9BuuO50IqlvhWQs9GHxkCmoph5BHjGiWKdg2t6Or8XoyAfRJiKag+uvd/fpASnNklfsB01WpZ4fhAe0VQ==", "path": "microsoft.extensions.primitives/9.0.7", "hashPath": "microsoft.extensions.primitives.9.0.7.nupkg.sha512"}, "Microsoft.Identity.Client/4.73.1": {"type": "package", "serviceable": true, "sha512": "sha512-NnDLS8QwYqO5ZZecL2oioi1LUqjh5Ewk4bMLzbgiXJbQmZhDLtKwLxL3DpGMlQAJ2G4KgEnvGPKa+OOgffeJbw==", "path": "microsoft.identity.client/4.73.1", "hashPath": "microsoft.identity.client.4.73.1.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.73.1": {"type": "package", "serviceable": true, "sha512": "sha512-xDztAiV2F0wI0W8FLKv5cbaBefyLD6JVaAsvgSN7bjWNCzGYzHbcOEIP5s4TJXUpQzMfUyBsFl1mC6Zmgpz0PQ==", "path": "microsoft.identity.client.extensions.msal/4.73.1", "hashPath": "microsoft.identity.client.extensions.msal.4.73.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-seOFPaBQh2K683eFujAuDsrO2XbOA+SvxRli+wu7kl+ZymuGQzjmmUKfyFHmDazpPOBnmOX1ZnjX7zFDZHyNIA==", "path": "microsoft.identitymodel.abstractions/7.5.0", "hashPath": "microsoft.identitymodel.abstractions.7.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-mfyiGptbcH+oYrzAtWWwuV+7MoM0G0si+9owaj6DGWInhq/N/KDj/pWHhq1ShdmBu332gjP+cppjgwBpsOj7Fg==", "path": "microsoft.identitymodel.jsonwebtokens/7.5.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-3BInZEajJvnTDP/YRrmJ3Fyw8XAWWR9jG+3FkhhzRJJYItVL+BEH9qlgxSmtrxp7S7N6TOv+Y+X8BG61viiehQ==", "path": "microsoft.identitymodel.logging/7.5.0", "hashPath": "microsoft.identitymodel.logging.7.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-ugyb0Nm+I+UrHGYg28mL8oCV31xZrOEbs8fQkcShUoKvgk22HroD2odCnqEf56CoAFYTwoDExz8deXzrFC+TyA==", "path": "microsoft.identitymodel.protocols/7.5.0", "hashPath": "microsoft.identitymodel.protocols.7.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-/U3I/8uutTqZr2n/zt0q08bluYklq+5VWP7ZuOGpTUR1ln5bSbrexAzdSGzrhxTxNNbHMCU8Mn2bNQvcmehAxg==", "path": "microsoft.identitymodel.protocols.openidconnect/7.5.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-owe33wqe0ZbwBxM3D90I0XotxNyTdl85jud03d+OrUOJNnTiqnYePwBk3WU9yW0Rk5CYX+sfSim7frmu6jeEzQ==", "path": "microsoft.identitymodel.tokens/7.5.0", "hashPath": "microsoft.identitymodel.tokens.7.5.0.nupkg.sha512"}, "Microsoft.NET.StringTools/17.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-N0ZIanl1QCgvUumEL1laasU0a7sOE5ZwLZVTn0pAePnfhq8P7SvTjF8Axq+CnavuQkmdQpGNXQ1efZtu5kDFbA==", "path": "microsoft.net.stringtools/17.6.3", "hashPath": "microsoft.net.stringtools.17.6.3.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"type": "package", "serviceable": true, "sha512": "sha512-vl5a2URJYCO5m+aZZtNlAXAMz28e2pUotRuoHD7RnCWOCeoyd8hWp5ZBaLNYq4iEj2oeJx5ZxiSboAjVmB20Qg==", "path": "microsoft.visualstudio.threading.only/17.13.61", "hashPath": "microsoft.visualstudio.threading.only.17.13.61.nupkg.sha512"}, "Microsoft.VisualStudio.Validation/17.8.8": {"type": "package", "serviceable": true, "sha512": "sha512-rWXThIpyQd4YIXghNkiv2+VLvzS+MCMKVRDR0GAMlflsdo+YcAN2g2r5U1Ah98OFjQMRexTFtXQQ2LkajxZi3g==", "path": "microsoft.visualstudio.validation/17.8.8", "hashPath": "microsoft.visualstudio.validation.17.8.8.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "MinVer/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+/SsmiySsXJlvQLCGBqaZKNVt3s/Y/HbAdwtop7Km2CnuZbaScoqkWJEBQ5Cy9ebkn6kCYKrHsXgwrFdTgcb3g==", "path": "minver/6.0.0", "hashPath": "minver.6.0.0.nupkg.sha512"}, "MongoDB.Bson/3.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-0fBbXqO/ADhWLcgq0cUNizcKEFAuz5klxUc5CWTWV8vdnlOl5QTeAI7Kh64bCoz654ZN7WWOjz9WRw0QGHcqQA==", "path": "mongodb.bson/3.4.0", "hashPath": "mongodb.bson.3.4.0.nupkg.sha512"}, "MongoDB.Driver/3.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-+8vGdlbbzfPfQzCI00nPX59vH+5Nfumb2lyXf7xdIcAa05JPBdXFc/vWw0rPQOqIWYWfTmvQsxUgUXyzk0AKGw==", "path": "mongodb.driver/3.4.0", "hashPath": "mongodb.driver.3.4.0.nupkg.sha512"}, "MySqlConnector/2.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-FYTnvNBbNSZotSWUerK22Ztsc3vx3wBRZ4veavkUbfCBNthtCr3CH5Wpoo4l5gBD2TmQD9JbL33Gf9kWko1yYw==", "path": "mysqlconnector/2.3.1", "hashPath": "mysqlconnector.2.3.1.nupkg.sha512"}, "Nerdbank.Streams/2.12.87": {"type": "package", "serviceable": true, "sha512": "sha512-oDKOeKZ865I5X8qmU3IXMyrAnssYEiYWTobPGdrqubN3RtTzEHIv+D6fwhdcfrdhPJzHjCkK/ORztR/IsnmA6g==", "path": "nerdbank.streams/2.12.87", "hashPath": "nerdbank.streams.2.12.87.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Npgsql/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-6WEmzsQJCZAlUG1pThKg/RmeF6V+I0DmBBBE/8YzpRtEzhyZzKcK7ulMANDm5CkxrALBEC8H+5plxHWtIL7xnA==", "path": "npgsql/8.0.3", "hashPath": "npgsql.8.0.3.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "Polly.Core/8.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-ImAKLH6qVDjj0vzw+QxMYxxT/NhQrHK+sZE4GT5JbIfDBOrMDbE4we3BR6SqUQCJuKdjOKf3smUjxIgOUUfNVw==", "path": "polly.core/8.6.2", "hashPath": "polly.core.8.6.2.nupkg.sha512"}, "RabbitMQ.Client/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-y3c6ulgULScWthHw5PLM1ShHRLhxg0vCtzX/hh61gRgNecL3ZC3WoBW2HYHoXOVRqTl99Br9E7CZEytGZEsCyQ==", "path": "rabbitmq.client/7.1.2", "hashPath": "rabbitmq.client.7.1.2.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "Semver/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9jZCicsVgTebqkAujRWtC9J1A5EQVlu0TVKHcgoCuv345ve5DYf4D1MjhKEnQjdRZo6x/vdv6QQrYFs7ilGzLA==", "path": "semver/3.0.0", "hashPath": "semver.3.0.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "StackExchange.Redis/2.8.41": {"type": "package", "serviceable": true, "sha512": "sha512-EgtQzlucry9V2Gces1K19Xi8Sz/8DfoZlGQEmL/hSM/SXO/ucDS5RPmLzcmT489ZNGFbj9CGhlpscPLOXtYWwA==", "path": "stackexchange.redis/2.8.41", "hashPath": "stackexchange.redis.2.8.41.nupkg.sha512"}, "StreamJsonRpc/2.22.11": {"type": "package", "serviceable": true, "sha512": "sha512-TQcqBFswLNpdSJANjhxZmIIe0Yl0kGqzjZ+uHLdhrkxntofvNu6C53XPEEYQ3Wkj8AorKumkuv/VMvTH4BHOZw==", "path": "streamjsonrpc/2.22.11", "hashPath": "streamjsonrpc.2.22.11.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ClientModel/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyQUynSbLh8IVyETF2tN14IQwBYgif3gIBt2LAL3FSl0W5FIEJIog3sposTxdz23EqDN78LAQFpUxbaj1EsRJw==", "path": "system.clientmodel/1.5.0", "hashPath": "system.clientmodel.1.5.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-RVSM6wZUo6L2y6P3vN6gjUtyJ2IF2RVtrepF3J7nrDKfFQd5u/SnSUFclchYQis8/k5scHy9E+fVeKVQLnnkzw==", "path": "system.collections.immutable/1.7.0", "hashPath": "system.collections.immutable.1.7.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-dvjqKp+2LpGid6phzrdrS/2mmEPxFl3jE1+L7614q4ZChKbLJCpHXg6sBILlCCED1t//EE+un/UdAetzIMpqnw==", "path": "system.configuration.configurationmanager/9.0.4", "hashPath": "system.configuration.configurationmanager.9.0.4.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-AJ+9fyCtQUImntxAJ9l4PZiCd4iepuk4pm7Qcno7PBIWQnfXlvwKuFsGk2H+QyY69GUVzDP2heELW6ho5BCXUg==", "path": "system.diagnostics.eventlog/9.0.7", "hashPath": "system.diagnostics.eventlog.9.0.7.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-D0TtrWOfoPdyYSlvOGaU9F1QR+qrbgJ/4eiEsQkIz7YQKIKkGXQldXukn6cYG9OahSq5UVMvyAIObECpH6Wglg==", "path": "system.identitymodel.tokens.jwt/7.5.0", "hashPath": "system.identitymodel.tokens.jwt.7.5.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Hashing/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-2sMrwd/AHYipeiOwYcoqD99c4U8F2x7GalonEYxkHUvTLNzE9Pxx2lz8ygS9cOqyDsruYARZAYL5pPVFh/s3lw==", "path": "system.io.hashing/9.0.7", "hashPath": "system.io.hashing.9.0.7.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B<PERSON><PERSON>uec3jV23EMRDeR7Dr1/qhx7369dZzJ9IWy2xylvb4YfXsrUxspWc4UWYid/tj4zZK58uGZqn2WQiaDMhmAg==", "path": "system.memory.data/8.0.1", "hashPath": "system.memory.data.8.0.1.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cUFTcMlz/Qw9s90b2wnWSCvHdjv51Bau9FQqhsr4TlwSe1OX+7SoXUqphis5G74MLOvMOCghxPPlEqOdCrVVGA==", "path": "system.security.cryptography.pkcs/9.0.4", "hashPath": "system.security.cryptography.pkcs.9.0.4.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-o94k2RKuAce3GeDMlUvIXlhVa1kWpJw95E6C9LwW0KlG0nj5+SgCiIxJ2Eroqb9sLtG1mEMbFttZIBZ13EJPvQ==", "path": "system.security.cryptography.protecteddata/9.0.4", "hashPath": "system.security.cryptography.protecteddata.9.0.4.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Json/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-xqC1HIbJMBFhrpYs76oYP+NAskNVjc6v73HqLal7ECRDPIp4oRU5pPavkD//vNactCn9DA2aaald/I5N+uZ5/g==", "path": "system.text.json/6.0.11", "hashPath": "system.text.json.6.0.11.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "YamlDotNet/16.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SgMOdxbz8X65z8hraIs6hOEdnkH6hESTAIUa7viEngHOYaH+6q5XJmwr1+yb9vJpNQ19hCQY69xbFsLtXpobQA==", "path": "yamldotnet/16.3.0", "hashPath": "yamldotnet.16.3.0.nupkg.sha512"}, "ZstdSharp.Port/0.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "path": "zstdsharp.port/0.7.3", "hashPath": "zstdsharp.port.0.7.3.nupkg.sha512"}}}