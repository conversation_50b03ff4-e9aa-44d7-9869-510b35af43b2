2025-08-06T22:02:10.8962144+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Version: 1.7.3+5abdae1f2e07071c4e81b27ac262f241708ec3cf
2025-08-06T22:02:10.9054115+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging mode: asynchronous
2025-08-06T22:02:10.9062768+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging level: Information
2025-08-06T22:02:10.9062808+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION CreateBuilderAsync entry time: 22:02:10.872
2025-08-06T22:02:10.9109894+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION PID: 40256
2025-08-06T22:02:10.9110172+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime information: win-x64 - .NET 9.0.7
2025-08-06T22:02:10.9110267+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime location: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.7\System.Private.CoreLib.dll
2025-08-06T22:02:10.9110358+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION IsDynamicCodeSupported: True
2025-08-06T22:02:10.9110720+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Test module: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Miller.WMS.Edge.Tests.dll
2025-08-06T22:02:10.9111330+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Command line arguments: '--server --diagnostic --diagnostic-verbosity Information --diagnostic-output-directory C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Log --results-directory C:\_\Miller_Github\Miller.WMS\TestResults --client-port 59493'
2025-08-06T22:02:10.9122562+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION TESTINGPLATFORM_DEFAULT_HANG_TIMEOUT: ''
2025-08-06T22:02:10.9213160+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting RegisterEnvironmentVariablesConfigurationSource: 'True'
2025-08-06T22:02:10.9331585+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting PlatformExitProcessOnUnhandledException: 'False', config file: False environment variable: 
2025-08-06T22:02:11.1187373+00:00 Microsoft.Testing.Platform.Requests.TestHostTestFrameworkInvoker INFORMATION Test framework UID: '30ea7c6e-dd24-4152-a360-1387158cd41d' Version: '3.0.0' DisplayName: 'xUnit.net v3 Microsoft.Testing.Platform test framework' Description: 'xUnit.net v3 Microsoft.Testing.Platform test framework'
2025-08-06T22:02:11.1220898+00:00 xUnit.net INFORMATION xUnit.net v3 In-Process Runner v3.0.0+d0213fc4e5 (64-bit .NET 9.0.7)
2025-08-06T22:02:11.1586523+00:00 xUnit.net INFORMATION   Discovering: Miller.WMS.Edge.Tests
2025-08-06T22:02:11.1909520+00:00 xUnit.net INFORMATION   Discovered:  Miller.WMS.Edge.Tests
2025-08-06T22:02:11.2089176+00:00 xUnit.net INFORMATION   Starting:    Miller.WMS.Edge.Tests
2025-08-06T22:06:11.5747466+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.ApiTests.GetApiResourceRootReturnsOkStatusCode [FAIL]
2025-08-06T22:06:11.5757571+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T22:06:11.5757741+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T22:06:11.5763575+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T22:06:11.5768662+00:00 xUnit.net INFORMATION         
2025-08-06T22:06:11.5768812+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T22:06:11.5769136+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T22:06:27.0711223+00:00 xUnit.net INFORMATION   Finished:    Miller.WMS.Edge.Tests
2025-08-06T22:06:27.0766180+00:00 xUnit.net INFORMATION === TEST EXECUTION SUMMARY ===
2025-08-06T22:06:27.0825188+00:00 xUnit.net INFORMATION    Miller.WMS.Edge.Tests  Total: 1, Errors: 0, Failed: 1, Skipped: 0, Not Run: 0, Time: 255.866s
