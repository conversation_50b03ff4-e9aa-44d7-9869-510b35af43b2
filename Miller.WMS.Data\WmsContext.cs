﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Miller.WMS.Domain;

namespace Miller.WMS.Shared.Data;

public class WmsContext : DbContext
{
    public WmsContext(DbContextOptions<WmsContext> options) : base(options) { }

    public DbSet<Organization> Organizations { get; set; }
    public DbSet<Facility> Facilities { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<UserFacilityRole> UserFacilityRoles { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<UserFacilityRole>()
            .<PERSON><PERSON><PERSON>(ufr => new { ufr.UserId, ufr.FacilityId });

        modelBuilder.Entity<UserFacilityRole>()
            .HasOne(ufr => ufr.User)
            .WithMany(u => u.UserFacilityRoles)
            .HasForeignKey(ufr => ufr.UserId);

        modelBuilder.Entity<UserFacilityRole>()
            .HasOne(ufr => ufr.Facility)
            .WithMany(f => f.UserFacilityRoles)
            .HasForeignKey(ufr => ufr.FacilityId);
    }
}

/// <summary>
/// Used purely for design-time services such as migrations.
/// </summary>
public class WmsContextFactory : IDesignTimeDbContextFactory<WmsContext>
{
    public WmsContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<WmsContext>();
        optionsBuilder.UseNpgsql();

        return new WmsContext(optionsBuilder.Options);
    }
}