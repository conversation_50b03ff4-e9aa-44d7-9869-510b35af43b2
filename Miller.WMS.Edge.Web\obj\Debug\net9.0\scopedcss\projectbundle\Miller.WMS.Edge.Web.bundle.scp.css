/* _content/Miller.WMS.Edge.Web/Components/Layout/MainLayout.razor.rz.scp.css */
.page[b-b72r714n5c] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-b72r714n5c] {
    flex: 1;
}

.sidebar[b-b72r714n5c] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-b72r714n5c] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-b72r714n5c]  a, .top-row[b-b72r714n5c]  .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row[b-b72r714n5c]  a:hover, .top-row[b-b72r714n5c]  .btn-link:hover {
        text-decoration: underline;
    }

    .top-row[b-b72r714n5c]  a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row[b-b72r714n5c] {
        justify-content: space-between;
    }

    .top-row[b-b72r714n5c]  a, .top-row[b-b72r714n5c]  .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-b72r714n5c] {
        flex-direction: row;
    }

    .sidebar[b-b72r714n5c] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-b72r714n5c] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth[b-b72r714n5c]  a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row[b-b72r714n5c], article[b-b72r714n5c] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}

#blazor-error-ui[b-b72r714n5c] {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss[b-b72r714n5c] {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
/* _content/Miller.WMS.Edge.Web/Components/Layout/NavMenu.razor.rz.scp.css */
.navbar-toggler[b-d5t60kbsz1] {
    appearance: none;
    cursor: pointer;
    width: 3.5rem;
    height: 2.5rem;
    color: white;
    position: absolute;
    top: 0.5rem;
    right: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") no-repeat center/1.75rem rgba(255, 255, 255, 0.1);
}

.navbar-toggler:checked[b-d5t60kbsz1] {
    background-color: rgba(255, 255, 255, 0.5);
}

.top-row[b-d5t60kbsz1] {
    min-height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand[b-d5t60kbsz1] {
    font-size: 1.1rem;
}

.bi[b-d5t60kbsz1] {
    display: inline-block;
    position: relative;
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    top: -1px;
    background-size: cover;
}

.bi-house-door-fill[b-d5t60kbsz1] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-house-door-fill' viewBox='0 0 16 16'%3E%3Cpath d='M6.5 14.5v-3.505c0-.245.25-.495.5-.495h2c.25 0 .5.25.5.5v3.5a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.146-.354L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.354 1.146a.5.5 0 0 0-.708 0l-6 6A.5.5 0 0 0 1.5 7.5v7a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5Z'/%3E%3C/svg%3E");
}

.bi-plus-square-fill[b-d5t60kbsz1] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-plus-square-fill' viewBox='0 0 16 16'%3E%3Cpath d='M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm6.5 4.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3a.5.5 0 0 1 1 0z'/%3E%3C/svg%3E");
}

.bi-list-nested[b-d5t60kbsz1] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-list-nested' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M4.5 11.5A.5.5 0 0 1 5 11h10a.5.5 0 0 1 0 1H5a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 3 7h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 1 3h10a.5.5 0 0 1 0 1H1a.5.5 0 0 1-.5-.5z'/%3E%3C/svg%3E");
}

.nav-item[b-d5t60kbsz1] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type[b-d5t60kbsz1] {
        padding-top: 1rem;
    }

    .nav-item:last-of-type[b-d5t60kbsz1] {
        padding-bottom: 1rem;
    }

    .nav-item[b-d5t60kbsz1]  a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

.nav-item[b-d5t60kbsz1]  a.active {
    background-color: rgba(255,255,255,0.37);
    color: white;
}

.nav-item[b-d5t60kbsz1]  a:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

.nav-scrollable[b-d5t60kbsz1] {
    display: none;
}

.navbar-toggler:checked ~ .nav-scrollable[b-d5t60kbsz1] {
    display: block;
}

@media (min-width: 641px) {
    .navbar-toggler[b-d5t60kbsz1] {
        display: none;
    }

    .nav-scrollable[b-d5t60kbsz1] {
        /* Never collapse the sidebar for wide screens */
        display: block;

        /* Allow sidebar to scroll for tall menus */
        height: calc(100vh - 3.5rem);
        overflow-y: auto;
    }
}
