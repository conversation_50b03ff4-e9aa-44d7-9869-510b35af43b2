2025-08-06T14:01:09.7417296+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Version: 1.6.2+88f8ce447cd12b629fcfe5e61d80dcc0c8cab8ec
2025-08-06T14:01:09.7507677+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging mode: asynchronous
2025-08-06T14:01:09.7517045+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging level: Information
2025-08-06T14:01:09.7517098+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION CreateBuilderAsync entry time: 14:01:09.720
2025-08-06T14:01:09.7552921+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION PID: 32696
2025-08-06T14:01:09.7553269+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime information: win-x64 - .NET 9.0.7
2025-08-06T14:01:09.7553372+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime location: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.7\System.Private.CoreLib.dll
2025-08-06T14:01:09.7553451+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION IsDynamicCodeSupported: True
2025-08-06T14:01:09.7553481+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Test module: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Miller.WMS.Edge.Tests.dll
2025-08-06T14:01:09.7554152+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Command line arguments: '--server --diagnostic --diagnostic-verbosity Information --diagnostic-output-directory C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Log --results-directory C:\_\Miller_Github\Miller.WMS\TestResults --client-port 59645'
2025-08-06T14:01:09.7564095+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION TESTINGPLATFORM_DEFAULT_HANG_TIMEOUT: ''
2025-08-06T14:01:09.7656416+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting RegisterEnvironmentVariablesConfigurationSource: 'True'
2025-08-06T14:01:09.7750489+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting PlatformExitProcessOnUnhandledException: 'False', config file: False environment variable:  VSTest mode: False
2025-08-06T14:01:09.9276933+00:00 Microsoft.Testing.Platform.Requests.TestHostTestFrameworkInvoker INFORMATION Test framework UID: '30ea7c6e-dd24-4152-a360-1387158cd41d' Version: '2.0.0' DisplayName: 'xUnit.net v3 Microsoft.Testing.Platform test framework' Description: 'xUnit.net v3 Microsoft.Testing.Platform test framework'
2025-08-06T14:01:09.9330197+00:00 xUnit.net INFORMATION xUnit.net v3 In-Process Runner v2.0.0+229879b765 (64-bit .NET 9.0.7)
2025-08-06T14:01:09.9575845+00:00 xUnit.net INFORMATION   Discovering: Miller.WMS.Edge.Tests
2025-08-06T14:01:09.9865549+00:00 xUnit.net INFORMATION   Discovered:  Miller.WMS.Edge.Tests
2025-08-06T14:01:09.9993673+00:00 xUnit.net INFORMATION   Starting:    Miller.WMS.Edge.Tests
2025-08-06T14:02:20.3065079+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CDCTests.CDCServiceStartsSuccessfully [FAIL]
2025-08-06T14:02:20.3068149+00:00 xUnit.net INFORMATION       System.TimeoutException : The operation has timed out.
2025-08-06T14:02:20.3069922+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T14:02:20.3074890+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(71,0): at Miller.WMS.Edge.Tests.AspireTestFixture.WaitForResourceHealthyAsync(String resourceName, CancellationToken cancellationToken)
2025-08-06T14:02:20.3075176+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\CDCTests.cs(28,0): at Miller.WMS.Edge.Tests.CDCTests.CDCServiceStartsSuccessfully()
2025-08-06T14:02:20.3075209+00:00 xUnit.net INFORMATION         --- End of stack trace from previous location ---
2025-08-06T14:02:35.8398344+00:00 xUnit.net INFORMATION   Finished:    Miller.WMS.Edge.Tests
2025-08-06T14:02:35.8440256+00:00 xUnit.net INFORMATION === TEST EXECUTION SUMMARY ===
2025-08-06T14:02:35.8481916+00:00 xUnit.net INFORMATION    Miller.WMS.Edge.Tests  Total: 1, Errors: 0, Failed: 1, Skipped: 0, Not Run: 0, Time: 85.842s
