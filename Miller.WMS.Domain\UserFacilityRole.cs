﻿using Miller.WMS.Domain;

namespace Miller.WMS.Domain;

public class UserFacilityRole
{
    public int UserId { get; set; }
    public User User { get; set; }
    public int FacilityId { get; set; }
    public Facility Facility { get; set; }
    public string Role { get; set; }

    public UserFacilityRole()
    {
        User = new User();
        Facility = new Facility();
        Role = string.Empty;
    }
}