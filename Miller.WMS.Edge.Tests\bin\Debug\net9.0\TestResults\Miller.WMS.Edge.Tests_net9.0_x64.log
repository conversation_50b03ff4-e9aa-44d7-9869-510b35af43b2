xUnit.net v3 Microsoft.Testing.Platform Runner v3.0.0+d0213fc4e5 (64-bit .NET 9.0.7)

info: Aspire.Hosting.DistributedApplication[0]
      Aspire version: 9.4.0+3661f0452eab4049b2fa7b8167bc8e53298e358c
info: Aspire.Hosting.DistributedApplication[0]
      Distributed application starting.
info: Aspire.Hosting.DistributedApplication[0]
      Application host directory is: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.AppHost
dbug: Aspire.Hosting.Cli.BackchannelService[0]
      Backchannel socket path was not specified.
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource username/username changed state: Waiting -> Active
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource password/password changed state: Waiting -> Active
[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(3s)

dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-edge-cache/wms-edge-cache-vztreuaf changed state: Starting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-psql-dbgate/wms-core-psql-dbgate-renvfers changed state: Starting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-psql/wms-core-psql-ceba9998 changed state: Starting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-search/wms-core-search-ceba9998 changed state: Starting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms/wms changed state: Starting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-dataservice/wms-core-dataservice-wgmjzztq changed state: Starting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-cdc/wms-core-cdc-nxdcfbmg changed state: Starting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-edge-web/wms-edge-web-fynbpsas changed state: Starting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-edge-api/wms-edge-api-pphmbggs changed state: Starting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-cdc/wms-core-cdc-nxdcfbmg changed state: Starting -> Waiting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-edge-web/wms-edge-web-fynbpsas changed state: Starting -> Waiting
info: Miller.WMS.Edge.AppHost.Resources.wms-core-cdc[0]
      1: 2025-08-08T21:56:44.0545323Z Waiting for resource 'wms-core-psql' to enter the 'Running' state.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      1: 2025-08-08T21:56:44.0543979Z Waiting for resource 'wms-core-psql' to enter the 'Running' state.
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-dataservice/wms-core-dataservice-wgmjzztq changed state: Starting -> Waiting
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      1: 2025-08-08T21:56:44.0545196Z Waiting for resource 'wms-edge-cache' to enter the 'Running' state.
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Waiting for resource 'wms-edge-web' to match predicate.
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Waiting for resource 'wms-core-cdc' to match predicate.
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Waiting for resource 'wms-core-dataservice' to match predicate.
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      2: 2025-08-08T21:56:44.0735107Z Waiting for resource 'wms-edge-api' to enter the 'Running' state.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      2: 2025-08-08T21:56:44.0734431Z Waiting for resource 'wms' to enter the 'Running' state.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-cdc[0]
      2: 2025-08-08T21:56:44.0734721Z Waiting for resource 'wms' to enter the 'Running' state.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-cdc[0]
      3: 2025-08-08T21:56:44.0740277Z Waiting for resource 'wms-core-search' to enter the 'Running' state.
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-psql-dbgate/wms-core-psql-dbgate-renvfers changed state: Starting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-edge-cache/wms-edge-cache-vztreuaf changed state: Starting
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-edge-api/wms-edge-api-pphmbggs changed state: Running
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Starting health monitoring for resource 'wms-edge-api'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-edge-api' health checks to monitor: wms-edge-api_https_/health_200_check
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      3: 2025-08-08T21:56:44.5075880Z Waiting for resource 'wms-edge-api' to become healthy.
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-search/wms-core-search-ceba9998 changed state: Running
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Starting health monitoring for resource 'wms-core-search'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-core-search' health checks to monitor: wms-core-search_check
info: Miller.WMS.Edge.AppHost.Resources.wms-core-cdc[0]
      4: 2025-08-08T21:56:45.0711847Z Waiting for resource 'wms-core-search' to become healthy.
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-psql/wms-core-psql-ceba9998 changed state: Running
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms/wms changed state: Running
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Starting health monitoring for resource 'wms-core-psql'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Starting health monitoring for resource 'wms'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-cdc[0]
      5: 2025-08-08T21:56:45.0855589Z Waiting for resource 'wms-core-psql' to become healthy.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-cdc[0]
      6: 2025-08-08T21:56:45.0856362Z Waiting for resource 'wms' to become healthy.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      3: 2025-08-08T21:56:45.0856182Z Waiting for resource 'wms' to become healthy.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      4: 2025-08-08T21:56:45.0856378Z Waiting for resource 'wms-core-psql' to become healthy.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-core-psql' health checks to monitor: wms-core-psql_check
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms' health checks to monitor: wms-core-psql_check, wms_check
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-cache[0]
      1: 2025-08-08T21:56:45.3750000Z eecea760345116f05ff2e2706a1fa1222827045f318dcad0234e62d9f13e7ee5
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql-dbgate[0]
      1: 2025-08-08T21:56:45.4810000Z c1f21df92c3af28975dd84a1f3a5188801f4a99e9a50d571044c983d0e793f80
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      1: 2025-08-08T21:20:34.0000000Z Created elasticsearch keystore in /usr/share/elasticsearch/config/elasticsearch.keystore
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      2: 2025-08-08T21:20:43.0000000Z CompileCommand: dontinline java/lang/invoke/MethodHandle.setAsTypeCache bool dontinline = true
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      3: 2025-08-08T21:20:43.0000000Z CompileCommand: dontinline java/lang/invoke/MethodHandle.asTypeUncached bool dontinline = true
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      4: 2025-08-08T21:20:45.0000000Z {"@timestamp":"2025-08-08T21:20:45.940Z", "log.level": "INFO", "message":"vec_caps=1", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.nativeaccess.jdk.JdkVectorLibrary","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      5: 2025-08-08T21:20:45.0000000Z {"@timestamp":"2025-08-08T21:20:45.975Z", "log.level": "INFO", "message":"Using native vector library; to disable start with -Dorg.elasticsearch.nativeaccess.enableVectorLibrary=false", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.nativeaccess.NativeAccess","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      6: 2025-08-08T21:20:45.0000000Z {"@timestamp":"2025-08-08T21:20:45.998Z", "log.level": "INFO", "message":"Using [jdk] native provider and native methods for [Linux]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.nativeaccess.NativeAccess","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      7: 2025-08-08T21:20:46.0000000Z {"@timestamp":"2025-08-08T21:20:46.170Z", "log.level": "INFO", "message":"Java vector incubator API enabled; uses preferredBitSize=256; FMA enabled", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.apache.lucene.internal.vectorization.PanamaVectorizationProvider","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      8: 2025-08-08T21:20:46.0000000Z {"@timestamp":"2025-08-08T21:20:46.170Z", "log.level": "INFO", "message":"Bootstrapping java SecurityManager", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.jdk.JarHell","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      9: 2025-08-08T21:20:48.0000000Z {"@timestamp":"2025-08-08T21:20:48.213Z", "log.level": "INFO", "message":"version[8.17.3], pid[174], build[docker/a091390de485bd4b127884f7e565c0cad59b10d2/2025-02-28T10:07:26.089129809Z], OS[Linux/**********-microsoft-standard-WSL2/amd64], JVM[Oracle Corporation/OpenJDK 64-Bit Server VM/23/23+37-2369]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.node.Node","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      10: 2025-08-08T21:20:48.0000000Z {"@timestamp":"2025-08-08T21:20:48.214Z", "log.level": "INFO", "message":"JVM home [/usr/share/elasticsearch/jdk], using bundled JDK [true]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.node.Node","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      11: 2025-08-08T21:20:48.0000000Z {"@timestamp":"2025-08-08T21:20:48.215Z", "log.level": "INFO", "message":"JVM arguments [-Des.networkaddress.cache.ttl=60, -Des.networkaddress.cache.negative.ttl=10, -XX:+AlwaysPreTouch, -Xss1m, -Djava.awt.headless=true, -Dfile.encoding=UTF-8, -Djna.nosys=true, -XX:-OmitStackTraceInFastThrow, -Dio.netty.noUnsafe=true, -Dio.netty.noKeySetOptimization=true, -Dio.netty.recycler.maxCapacityPerThread=0, -Dlog4j.shutdownHookEnabled=false, -Dlog4j2.disable.jmx=true, -Dlog4j2.formatMsgNoLookups=true, -Djava.locale.providers=CLDR, -Des.distribution.type=docker, --enable-native-access=org.elasticsearch.nativeaccess,org.apache.lucene.core, -Des.cgroups.hierarchy.override=/, -XX:ReplayDataFile=logs/replay_pid%p.log, -Djava.security.manager=allow, -XX:+UseG1GC, -Djava.io.tmpdir=/tmp/elasticsearch-2720424488212028252, --add-modules=jdk.incubator.vector, -XX:CompileCommand=dontinline,java/lang/invoke/MethodHandle.setAsTypeCache, -XX:CompileCommand=dontinline,java/lang/invoke/MethodHandle.asTypeUncached, -XX:+HeapDumpOnOutOfMemoryError, -XX:+ExitOnOutOfMemoryError, -XX:HeapDumpPath=data, -XX:ErrorFile=logs/hs_err_pid%p.log, -Xlog:gc*,gc+age=trace,safepoint:file=logs/gc.log:utctime,level,pid,tags:filecount=32,filesize=64m, -Xms3835m, -Xmx3835m, -XX:MaxDirectMemorySize=**********, -XX:G1HeapRegionSize=4m, -XX:InitiatingHeapOccupancyPercent=30, -XX:G1ReservePercent=15, --module-path=/usr/share/elasticsearch/lib, --add-modules=jdk.net, --add-modules=ALL-MODULE-PATH, -Djdk.module.main=org.elasticsearch.server]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.node.Node","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      12: 2025-08-08T21:20:48.0000000Z {"@timestamp":"2025-08-08T21:20:48.215Z", "log.level": "INFO", "message":"Default Locale [en_US]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.node.Node","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      13: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.101Z", "log.level": "INFO", "message":"loaded module [repository-url]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      14: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.102Z", "log.level": "INFO", "message":"loaded module [rest-root]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      15: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.110Z", "log.level": "INFO", "message":"loaded module [x-pack-core]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      16: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.111Z", "log.level": "INFO", "message":"loaded module [x-pack-redact]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      17: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.112Z", "log.level": "INFO", "message":"loaded module [ingest-user-agent]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      18: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.112Z", "log.level": "INFO", "message":"loaded module [x-pack-async-search]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      19: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.113Z", "log.level": "INFO", "message":"loaded module [x-pack-monitoring]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      20: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.113Z", "log.level": "INFO", "message":"loaded module [repository-s3]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      21: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.114Z", "log.level": "INFO", "message":"loaded module [x-pack-esql-core]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      22: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.114Z", "log.level": "INFO", "message":"loaded module [x-pack-analytics]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      23: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.114Z", "log.level": "INFO", "message":"loaded module [x-pack-ent-search]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      24: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.115Z", "log.level": "INFO", "message":"loaded module [x-pack-autoscaling]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      25: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.115Z", "log.level": "INFO", "message":"loaded module [lang-painless]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      26: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.116Z", "log.level": "INFO", "message":"loaded module [x-pack-ml]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      27: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.116Z", "log.level": "INFO", "message":"loaded module [legacy-geo]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      28: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.117Z", "log.level": "INFO", "message":"loaded module [lang-mustache]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      29: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.117Z", "log.level": "INFO", "message":"loaded module [logsdb]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      30: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.118Z", "log.level": "INFO", "message":"loaded module [x-pack-ql]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      31: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.118Z", "log.level": "INFO", "message":"loaded module [rank-rrf]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      32: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.119Z", "log.level": "INFO", "message":"loaded module [analysis-common]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      33: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.119Z", "log.level": "INFO", "message":"loaded module [health-shards-availability]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      34: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.120Z", "log.level": "INFO", "message":"loaded module [transport-netty4]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      35: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.120Z", "log.level": "INFO", "message":"loaded module [aggregations]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      36: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.121Z", "log.level": "INFO", "message":"loaded module [ingest-common]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      37: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.121Z", "log.level": "INFO", "message":"loaded module [frozen-indices]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      38: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.122Z", "log.level": "INFO", "message":"loaded module [x-pack-identity-provider]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      39: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.122Z", "log.level": "INFO", "message":"loaded module [x-pack-shutdown]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      40: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.123Z", "log.level": "INFO", "message":"loaded module [x-pack-text-structure]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      41: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.124Z", "log.level": "INFO", "message":"loaded module [snapshot-repo-test-kit]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      42: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.125Z", "log.level": "INFO", "message":"loaded module [ml-package-loader]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      43: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.126Z", "log.level": "INFO", "message":"loaded module [kibana]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      44: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.126Z", "log.level": "INFO", "message":"loaded module [constant-keyword]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      45: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.126Z", "log.level": "INFO", "message":"loaded module [x-pack-logstash]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      46: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.127Z", "log.level": "INFO", "message":"loaded module [x-pack-graph]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      47: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.127Z", "log.level": "INFO", "message":"loaded module [x-pack-ccr]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      48: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.128Z", "log.level": "INFO", "message":"loaded module [x-pack-esql]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      49: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.128Z", "log.level": "INFO", "message":"loaded module [parent-join]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      50: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.128Z", "log.level": "INFO", "message":"loaded module [counted-keyword]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      51: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.129Z", "log.level": "INFO", "message":"loaded module [x-pack-enrich]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      52: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.130Z", "log.level": "INFO", "message":"loaded module [repositories-metering-api]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      53: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.130Z", "log.level": "INFO", "message":"loaded module [transform]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      54: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.130Z", "log.level": "INFO", "message":"loaded module [repository-azure]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      55: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.130Z", "log.level": "INFO", "message":"loaded module [dot-prefix-validation]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      56: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.131Z", "log.level": "INFO", "message":"loaded module [repository-gcs]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      57: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.132Z", "log.level": "INFO", "message":"loaded module [x-pack-otel-data]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      58: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.132Z", "log.level": "INFO", "message":"loaded module [spatial]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      59: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.132Z", "log.level": "INFO", "message":"loaded module [apm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      60: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.132Z", "log.level": "INFO", "message":"loaded module [mapper-extras]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      61: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.133Z", "log.level": "INFO", "message":"loaded module [mapper-version]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      62: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.134Z", "log.level": "INFO", "message":"loaded module [x-pack-rollup]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      63: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.134Z", "log.level": "INFO", "message":"loaded module [percolator]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      64: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.134Z", "log.level": "INFO", "message":"loaded module [x-pack-stack]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      65: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.135Z", "log.level": "INFO", "message":"loaded module [data-streams]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      66: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.135Z", "log.level": "INFO", "message":"loaded module [reindex]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      67: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.136Z", "log.level": "INFO", "message":"loaded module [rank-eval]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      68: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.136Z", "log.level": "INFO", "message":"loaded module [x-pack-security]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      69: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.137Z", "log.level": "INFO", "message":"loaded module [blob-cache]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      70: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.137Z", "log.level": "INFO", "message":"loaded module [searchable-snapshots]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      71: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.138Z", "log.level": "INFO", "message":"loaded module [x-pack-slm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      72: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.138Z", "log.level": "INFO", "message":"loaded module [x-pack-geoip-enterprise-downloader]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      73: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.138Z", "log.level": "INFO", "message":"loaded module [snapshot-based-recoveries]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      74: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.139Z", "log.level": "INFO", "message":"loaded module [x-pack-watcher]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      75: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.139Z", "log.level": "INFO", "message":"loaded module [old-lucene-versions]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      76: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.139Z", "log.level": "INFO", "message":"loaded module [x-pack-ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      77: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.140Z", "log.level": "INFO", "message":"loaded module [x-pack-voting-only-node]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      78: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.140Z", "log.level": "INFO", "message":"loaded module [x-pack-inference]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      79: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.141Z", "log.level": "INFO", "message":"loaded module [x-pack-deprecation]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      80: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.141Z", "log.level": "INFO", "message":"loaded module [x-pack-fleet]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      81: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.141Z", "log.level": "INFO", "message":"loaded module [x-pack-profiling]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      82: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.141Z", "log.level": "INFO", "message":"loaded module [x-pack-aggregate-metric]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      83: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.141Z", "log.level": "INFO", "message":"loaded module [x-pack-downsample]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      84: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.141Z", "log.level": "INFO", "message":"loaded module [ingest-geoip]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      85: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.142Z", "log.level": "INFO", "message":"loaded module [x-pack-write-load-forecaster]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      86: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.142Z", "log.level": "INFO", "message":"loaded module [search-business-rules]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      87: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.142Z", "log.level": "INFO", "message":"loaded module [wildcard]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      88: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.143Z", "log.level": "INFO", "message":"loaded module [ingest-attachment]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      89: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.144Z", "log.level": "INFO", "message":"loaded module [x-pack-apm-data]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      90: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.145Z", "log.level": "INFO", "message":"loaded module [x-pack-sql]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      91: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.146Z", "log.level": "INFO", "message":"loaded module [unsigned-long]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      92: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.146Z", "log.level": "INFO", "message":"loaded module [runtime-fields-common]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      93: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.148Z", "log.level": "INFO", "message":"loaded module [x-pack-async]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      94: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.148Z", "log.level": "INFO", "message":"loaded module [x-pack-kql]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      95: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.149Z", "log.level": "INFO", "message":"loaded module [vector-tile]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      96: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.149Z", "log.level": "INFO", "message":"loaded module [lang-expression]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      97: 2025-08-08T21:20:55.0000000Z {"@timestamp":"2025-08-08T21:20:55.150Z", "log.level": "INFO", "message":"loaded module [x-pack-eql]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.plugins.PluginsService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      98: 2025-08-08T21:20:58.0000000Z {"@timestamp":"2025-08-08T21:20:58.264Z", "log.level": "INFO", "message":"using [1] data paths, mounts [[/ (overlay)]], net usable_space [950gb], net total_space [1006.8gb], types [overlay]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.env.NodeEnvironment","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      99: 2025-08-08T21:20:58.0000000Z {"@timestamp":"2025-08-08T21:20:58.264Z", "log.level": "INFO", "message":"heap size [3.7gb], compressed ordinary object pointers [true]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.env.NodeEnvironment","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      100: 2025-08-08T21:20:58.0000000Z {"@timestamp":"2025-08-08T21:20:58.281Z", "log.level": "INFO", "message":"node name [ae8ed4e9b92a], node ID [kgOIneAQRw-3dLYDiodD1g], cluster name [docker-cluster], roles [master, data_warm, data_content, transform, data_hot, ml, data_frozen, ingest, data_cold, data, remote_cluster_client]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.node.Node","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      101: 2025-08-08T21:21:04.0000000Z {"@timestamp":"2025-08-08T21:21:04.750Z", "log.level": "INFO", "message":"using rate limit [40mb] with [default=40mb, read=0b, write=0b, max=0b]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.indices.recovery.RecoverySettings","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      102: 2025-08-08T21:21:05.0000000Z {"@timestamp":"2025-08-08T21:21:05.211Z", "log.level": "INFO", "message":"Registered local node features [cluster.stats.source_modes, data_stream.auto_sharding, data_stream.lifecycle.global_retention, data_stream.rollover.lazy, desired_node.version_deprecated, esql.agg_values, esql.async_query, esql.base64_decode_encode, esql.casting_operator, esql.counter_types, esql.disable_nullable_opts, esql.from_options, esql.metadata_fields, esql.metrics_counter_fields, esql.mv_ordering_sorted_ascending, esql.mv_sort, esql.resolve_fields_api, esql.spatial_points_from_source, esql.spatial_shapes, esql.st_centroid_agg, esql.st_contains_within, esql.st_disjoint, esql.st_intersects, esql.st_x_y, esql.string_literal_auto_casting, esql.string_literal_auto_casting_extended, esql.timespan_abbreviations, features_supported, file_settings, flattened.ignore_above_support, geoip.downloader.database.configuration, get_database_configuration_action.multi_node, health.dsl.info, health.extended_repository_indicator, knn_retriever_supported, license-trial-independent-version, logsdb_telemetry, logsdb_telemetry_stats, mapper.boolean_dimension, mapper.flattened.ignore_above_with_arrays_support, mapper.ignore_above_index_level_setting, mapper.index_sorting_on_nested, mapper.keyword_dimension_ignore_above, mapper.keyword_normalizer_synthetic_source, mapper.pass_through_priority, mapper.query_index_mode, mapper.range.null_values_off_by_one_fix, mapper.segment_level_fields_stats, mapper.source.synthetic_source_copy_to_fix, mapper.source.synthetic_source_copy_to_inside_objects_fix, mapper.source.synthetic_source_fallback, mapper.source.synthetic_source_stored_fields_advance_fix, mapper.source.synthetic_source_with_copy_to_and_doc_values_false, mapper.subobjects_auto, mapper.subobjects_auto_fixes, mapper.synthetic_source_keep, mapper.track_ignored_source, mapper.vectors.bbq, mapper.vectors.bit_vectors, mapper.vectors.int4_quantization, put_database_configuration_action.ipinfo, query_rule_list_types, query_rule_retriever_supported, query_rules.test, random_reranker_retriever_supported, repositories.supports_usage_stats, rest.capabilities_action, rest.local_only_capabilities, retrievers_supported, routing.boolean_routing_path, routing.multi_value_routing_path, rrf_retriever_composition_supported, rrf_retriever_supported, script.hamming, script.term_stats, search.vectors.k_param_supported, security.migration_framework, security.role_mapping_cleanup, security.roles_metadata_flattened, semantic_text.default_elser_2, semantic_text.search_inference_id, simulate.component.template.substitutions, simulate.index.template.substitutions, simulate.mapping.addition, simulate.mapping.validation, simulate.mapping.validation.templates, simulate.support.non.template.mapping, slm.interval_schedule, snapshot.repository_verify_integrity, standard_retriever_supported, stats.include_disk_thresholds, text_similarity_reranker_retriever_composition_supported, text_similarity_reranker_retriever_supported, tsdb.ts_routing_hash_doc_value_parse_byte_ref, unified_highlighter_matched_fields, usage.data_tiers.precalculate_stats]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.features.FeatureService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      103: 2025-08-08T21:21:05.0000000Z {"@timestamp":"2025-08-08T21:21:05.302Z", "log.level": "INFO", "message":"Updated default factory retention to [null]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.cluster.metadata.DataStreamGlobalRetentionSettings","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      104: 2025-08-08T21:21:05.0000000Z {"@timestamp":"2025-08-08T21:21:05.304Z", "log.level": "INFO", "message":"Updated max factory retention to [null]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.cluster.metadata.DataStreamGlobalRetentionSettings","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      105: 2025-08-08T21:21:05.0000000Z {"@timestamp":"2025-08-08T21:21:05.910Z", "log.level": "INFO", "message":"[controller/206] [Main.cc@123] controller (64 bit): Version 8.17.3 (Build 613f2dcc0c1dd3) Copyright (c) 2025 Elasticsearch BV", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"ml-cpp-log-tail-thread","log.logger":"org.elasticsearch.xpack.ml.process.logging.CppLogMessageHandler","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      106: 2025-08-08T21:21:06.0000000Z {"@timestamp":"2025-08-08T21:21:06.543Z", "log.level": "INFO", "message":"OTel ingest plugin is enabled", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.xpack.oteldata.OTelPlugin","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      107: 2025-08-08T21:21:06.0000000Z {"@timestamp":"2025-08-08T21:21:06.606Z", "log.level": "INFO", "message":"OpenTelemetry index template registry is enabled", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.xpack.core.template.YamlTemplateRegistry","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      108: 2025-08-08T21:21:06.0000000Z {"@timestamp":"2025-08-08T21:21:06.609Z", "log.level": "INFO", "message":"Sending apm metrics is disabled", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.telemetry.apm.APM","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      109: 2025-08-08T21:21:06.0000000Z {"@timestamp":"2025-08-08T21:21:06.609Z", "log.level": "INFO", "message":"Sending apm tracing is disabled", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.telemetry.apm.APM","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      110: 2025-08-08T21:21:06.0000000Z {"@timestamp":"2025-08-08T21:21:06.680Z", "log.level": "INFO", "message":"Security is enabled", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.xpack.security.Security","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      111: 2025-08-08T21:21:07.0000000Z {"@timestamp":"2025-08-08T21:21:07.188Z", "log.level": "INFO", "message":"parsed [0] roles from file [/usr/share/elasticsearch/config/roles.yml]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.xpack.security.authz.store.FileRolesStore","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      112: 2025-08-08T21:21:08.0000000Z {"@timestamp":"2025-08-08T21:21:08.092Z", "log.level": "INFO", "message":"Watcher initialized components at 2025-08-08T21:21:08.092Z", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.xpack.watcher.Watcher","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      113: 2025-08-08T21:21:08.0000000Z {"@timestamp":"2025-08-08T21:21:08.272Z", "log.level": "INFO", "message":"Profiling is enabled", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.xpack.profiling.ProfilingPlugin","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      114: 2025-08-08T21:21:08.0000000Z {"@timestamp":"2025-08-08T21:21:08.313Z", "log.level": "INFO", "message":"profiling index templates will not be installed or reinstalled", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.xpack.profiling.ProfilingPlugin","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      115: 2025-08-08T21:21:08.0000000Z {"@timestamp":"2025-08-08T21:21:08.354Z", "log.level": "INFO", "message":"APM ingest plugin is enabled", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.xpack.apmdata.APMPlugin","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      116: 2025-08-08T21:21:08.0000000Z {"@timestamp":"2025-08-08T21:21:08.455Z", "log.level": "INFO", "message":"apm index template registry is enabled", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.xpack.core.template.YamlTemplateRegistry","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      117: 2025-08-08T21:21:09.0000000Z {"@timestamp":"2025-08-08T21:21:09.450Z", "log.level": "INFO", "message":"creating NettyAllocator with the following configs: [name=elasticsearch_configured, chunk_size=1mb, suggested_max_allocation_size=1mb, factors={es.unsafe.use_netty_default_chunk_and_page_size=false, g1gc_enabled=true, g1gc_region_size=4mb}]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.transport.netty4.NettyAllocator","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      118: 2025-08-08T21:21:09.0000000Z {"@timestamp":"2025-08-08T21:21:09.529Z", "log.level": "INFO", "message":"using discovery type [single-node] and seed hosts providers [settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.discovery.DiscoveryModule","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      119: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.253Z", "log.level": "INFO", "message":"initialized", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.node.Node","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      120: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.253Z", "log.level": "INFO", "message":"starting ...", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.node.Node","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      121: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.283Z", "log.level": "INFO", "message":"persistent cache index loaded", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.xpack.searchablesnapshots.cache.full.PersistentCache","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      122: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.283Z", "log.level": "INFO", "message":"deprecation component started", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.xpack.deprecation.logging.DeprecationIndexingComponent","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      123: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.396Z", "log.level": "INFO", "message":"publish_address {*********:9300}, bound_addresses {[::]:9300}", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.transport.TransportService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      124: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.575Z", "log.level": "WARN", "message":"Transport SSL must be enabled if security is enabled. Please set [xpack.security.transport.ssl.enabled] to [true] or disable security by setting [xpack.security.enabled] to [false]; for more information see [https://www.elastic.co/guide/en/elasticsearch/reference/8.17/bootstrap-checks-xpack.html#bootstrap-checks-tls]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.bootstrap.BootstrapChecks","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      125: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.585Z", "log.level": "INFO", "message":"this node has not joined a bootstrapped cluster yet; [cluster.initial_master_nodes] is set to [ae8ed4e9b92a]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.cluster.coordination.ClusterBootstrapService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      126: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.594Z", "log.level": "INFO", "message":"setting initial configuration to VotingConfiguration{kgOIneAQRw-3dLYDiodD1g}", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.cluster.coordination.Coordinator","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      127: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.816Z", "log.level": "INFO", "message":"elected-as-master ([1] nodes joined in term 1)[_FINISH_ELECTION_, {ae8ed4e9b92a}{kgOIneAQRw-3dLYDiodD1g}{JRkxKYxYSjKgprYLF2Iqlw}{ae8ed4e9b92a}{*********}{*********:9300}{cdfhilmrstw}{8.17.3}{7000099-8521000} completing election], term: 1, version: 1, delta: master node changed {previous [], current [{ae8ed4e9b92a}{kgOIneAQRw-3dLYDiodD1g}{JRkxKYxYSjKgprYLF2Iqlw}{ae8ed4e9b92a}{*********}{*********:9300}{cdfhilmrstw}{8.17.3}{7000099-8521000}]}", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.service.MasterService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      128: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.878Z", "log.level": "INFO", "message":"cluster UUID set to [fuoVlTYdSEmG3S3og8IRtw]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][cluster_coordination][T#1]","log.logger":"org.elasticsearch.cluster.coordination.CoordinationState","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      129: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.924Z", "log.level": "INFO", "message":"master node changed {previous [], current [{ae8ed4e9b92a}{kgOIneAQRw-3dLYDiodD1g}{JRkxKYxYSjKgprYLF2Iqlw}{ae8ed4e9b92a}{*********}{*********:9300}{cdfhilmrstw}{8.17.3}{7000099-8521000}]}, term: 1, version: 1, reason: Publication{term=1, version=1}", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][clusterApplierService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.service.ClusterApplierService","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      130: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.967Z", "log.level": "INFO", "message":"node-join: [{ae8ed4e9b92a}{kgOIneAQRw-3dLYDiodD1g}{JRkxKYxYSjKgprYLF2Iqlw}{ae8ed4e9b92a}{*********}{*********:9300}{cdfhilmrstw}{8.17.3}{7000099-8521000}] with reason [completing election]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.coordination.NodeJoinExecutor","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      131: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.974Z", "log.level": "INFO", "message":"publish_address {*********:9200}, bound_addresses {[::]:9200}", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.http.AbstractHttpServerTransport","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      132: 2025-08-08T21:21:11.0000000Z {"@timestamp":"2025-08-08T21:21:11.989Z", "log.level": "INFO", "message":"started {ae8ed4e9b92a}{kgOIneAQRw-3dLYDiodD1g}{JRkxKYxYSjKgprYLF2Iqlw}{ae8ed4e9b92a}{*********}{*********:9300}{cdfhilmrstw}{8.17.3}{7000099-8521000}{xpack.installed=true, ml.config_version=12.0.0, ml.max_jvm_size=4022337536, ml.allocated_processors_double=14.0, ml.allocated_processors=14, ml.machine_memory=8043364352, transform.config_version=10.0.0}", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"main","log.logger":"org.elasticsearch.node.Node","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      133: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.015Z", "log.level": "WARN",  "data_stream.dataset":"deprecation.elasticsearch","data_stream.namespace":"default","data_stream.type":"logs","elasticsearch.event.category":"indices","event.code":"dot-prefix","message":"Index [.monitoring-ent-search-8-*] name begins with a dot (.), which is deprecated, and will not be allowed in a future Elasticsearch version." , "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"deprecation.elasticsearch","process.thread.name":"elasticsearch[ae8ed4e9b92a][generic][T#10]","log.logger":"org.elasticsearch.deprecation.validation.DotPrefixValidator","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      134: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.085Z", "log.level": "INFO", "message":"starting file watcher ...", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][clusterApplierService#updateTask][T#1]","log.logger":"org.elasticsearch.common.file.AbstractFileWatchingService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      135: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.095Z", "log.level": "INFO", "message":"file settings service up and running [tid=81]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[file-watcher[/usr/share/elasticsearch/config/operator/settings.json]]","log.logger":"org.elasticsearch.common.file.AbstractFileWatchingService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      136: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.096Z", "log.level": "INFO", "message":"setting file [/usr/share/elasticsearch/config/operator/settings.json] not found, initializing [file_settings] as empty", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[file-watcher[/usr/share/elasticsearch/config/operator/settings.json]]","log.logger":"org.elasticsearch.reservedstate.service.FileSettingsService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      137: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.096Z", "log.level": "INFO", "message":"recovered [0] indices into cluster_state", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.gateway.GatewayService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      138: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.333Z", "log.level": "INFO", "message":"adding template [.monitoring-es] for index patterns [.monitoring-es-7-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      139: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.345Z", "log.level": "INFO", "message":"adding template [.monitoring-kibana] for index patterns [.monitoring-kibana-7-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      140: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.371Z", "log.level": "INFO", "message":"adding template [.monitoring-beats] for index patterns [.monitoring-beats-7-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      141: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.380Z", "log.level": "INFO", "message":"adding template [.monitoring-alerts-7] for index patterns [.monitoring-alerts-7]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      142: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.392Z", "log.level": "INFO", "message":"adding template [.monitoring-logstash] for index patterns [.monitoring-logstash-7-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      143: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.415Z", "log.level": "INFO", "message":"adding component template [behavioral_analytics-events-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      144: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.441Z", "log.level": "INFO", "message":"adding index template [.monitoring-ent-search-mb] for index patterns [.monitoring-ent-search-8-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      145: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.454Z", "log.level": "INFO", "message":"adding index template [.monitoring-logstash-mb] for index patterns [.monitoring-logstash-8-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      146: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.460Z", "log.level": "INFO", "message":"adding index template [.ml-state] for index patterns [.ml-state*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      147: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.466Z", "log.level": "INFO", "message":"adding index template [search-acl-filter] for index patterns [.search-acl-filter-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      148: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.471Z", "log.level": "INFO", "message":"adding component template [elastic-connectors-sync-jobs-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      149: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.489Z", "log.level": "INFO", "message":"adding index template [.monitoring-kibana-mb] for index patterns [.monitoring-kibana-8-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      150: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.512Z", "log.level": "INFO", "message":"adding index template [.monitoring-beats-mb] for index patterns [.monitoring-beats-8-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      151: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.516Z", "log.level": "INFO", "message":"adding component template [elastic-connectors-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      152: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.528Z", "log.level": "INFO", "message":"adding component template [elastic-connectors-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      153: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.542Z", "log.level": "INFO", "message":"adding index template [.ml-notifications-000002] for index patterns [.ml-notifications-000002]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      154: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.552Z", "log.level": "INFO", "message":"adding component template [elastic-connectors-sync-jobs-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      155: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.562Z", "log.level": "INFO", "message":"adding index template [.ml-stats] for index patterns [.ml-stats-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      156: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.580Z", "log.level": "INFO", "message":"adding index template [.ml-anomalies-] for index patterns [.ml-anomalies-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      157: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.613Z", "log.level": "INFO", "message":"adding index template [.monitoring-es-mb] for index patterns [.monitoring-es-8-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      158: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.623Z", "log.level": "INFO", "message":"adding component template [metrics-otel@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      159: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.633Z", "log.level": "INFO", "message":"adding component template [semconv-resource-to-ecs@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      160: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.641Z", "log.level": "INFO", "message":"adding component template [ecs-tsdb@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      161: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.658Z", "log.level": "INFO", "message":"adding component template [traces-otel@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      162: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.673Z", "log.level": "INFO", "message":"adding component template [logs-otel@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      163: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.681Z", "log.level": "INFO", "message":"adding component template [otel@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      164: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.686Z", "log.level": "INFO", "message":"adding component template [metrics-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      165: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.689Z", "log.level": "INFO", "message":"adding component template [synthetics-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      166: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.695Z", "log.level": "INFO", "message":"adding component template [ecs@dynamic_templates]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      167: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.699Z", "log.level": "INFO", "message":"adding component template [logs-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      168: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.703Z", "log.level": "INFO", "message":"adding component template [metrics-tsdb-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      169: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.709Z", "log.level": "INFO", "message":"adding component template [synthetics-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      170: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.713Z", "log.level": "INFO", "message":"adding component template [metrics-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      171: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.717Z", "log.level": "INFO", "message":"adding component template [data-streams-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      172: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.721Z", "log.level": "INFO", "message":"adding component template [logs@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      173: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.725Z", "log.level": "INFO", "message":"adding component template [metrics@tsdb-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      174: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.733Z", "log.level": "INFO", "message":"adding component template [ecs@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      175: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.740Z", "log.level": "INFO", "message":"adding component template [traces@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      176: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.745Z", "log.level": "INFO", "message":"adding component template [data-streams@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      177: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.751Z", "log.level": "INFO", "message":"adding component template [synthetics@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      178: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.757Z", "log.level": "INFO", "message":"adding component template [metrics@settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      179: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.760Z", "log.level": "INFO", "message":"adding component template [traces@settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      180: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.764Z", "log.level": "INFO", "message":"adding component template [kibana-reporting@settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      181: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.770Z", "log.level": "INFO", "message":"adding component template [metrics@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      182: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.774Z", "log.level": "INFO", "message":"adding component template [synthetics@settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      183: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.781Z", "log.level": "INFO", "message":"adding index template [.slm-history-7] for index patterns [.slm-history-7*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      184: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.791Z", "log.level": "INFO", "message":"adding index template [ilm-history-7] for index patterns [ilm-history-7*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      185: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.803Z", "log.level": "INFO", "message":"adding index template [.watch-history-16] for index patterns [.watcher-history-16*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      186: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.807Z", "log.level": "INFO", "message":"adding component template [.deprecation-indexing-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      187: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.816Z", "log.level": "INFO", "message":"adding index template [.fleet-fileds-tohost-meta] for index patterns [.fleet-fileds-tohost-meta-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      188: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.825Z", "log.level": "INFO", "message":"adding index template [.fleet-fileds-tohost-data] for index patterns [.fleet-fileds-tohost-data-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      189: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.830Z", "log.level": "INFO", "message":"adding component template [.deprecation-indexing-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      190: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.838Z", "log.level": "INFO", "message":"adding index template [.fleet-fileds-fromhost-data] for index patterns [.fleet-fileds-fromhost-data-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      191: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.844Z", "log.level": "INFO", "message":"adding index template [.fleet-fileds-fromhost-meta] for index patterns [.fleet-fileds-fromhost-meta-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      192: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.846Z", "log.level": "INFO", "message":"adding component template [traces-apm.sampled-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      193: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.852Z", "log.level": "INFO", "message":"adding component template [apm@settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      194: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.857Z", "log.level": "INFO", "message":"adding component template [apm-10d@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      195: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.862Z", "log.level": "INFO", "message":"adding component template [apm-180d@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      196: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.868Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_destination.60m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      197: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.883Z", "log.level": "INFO", "message":"adding component template [metrics-apm.transaction@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      198: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.887Z", "log.level": "INFO", "message":"adding component template [apm-390d@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      199: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.893Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_destination@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      200: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.896Z", "log.level": "INFO", "message":"adding component template [metrics-apm.transaction.1m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      201: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.899Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_summary.1m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      202: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.903Z", "log.level": "INFO", "message":"adding component template [apm-90d@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      203: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.910Z", "log.level": "INFO", "message":"adding component template [apm@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      204: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.916Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_summary@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      205: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.921Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_transaction@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      206: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.926Z", "log.level": "INFO", "message":"adding component template [traces-apm.rum@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      207: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.930Z", "log.level": "INFO", "message":"adding component template [logs-apm.app-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      208: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.935Z", "log.level": "INFO", "message":"adding component template [metrics-apm.internal-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      209: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.941Z", "log.level": "INFO", "message":"adding component template [metrics-apm@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      210: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.951Z", "log.level": "INFO", "message":"adding component template [traces-apm@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      211: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.957Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_destination.1m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      212: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.960Z", "log.level": "INFO", "message":"adding component template [metrics-apm.transaction.10m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      213: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.963Z", "log.level": "INFO", "message":"adding component template [traces-apm.rum-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      214: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.965Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_summary.10m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      215: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.968Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_transaction.60m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      216: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.973Z", "log.level": "INFO", "message":"adding component template [traces-apm-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      217: 2025-08-08T21:21:12.0000000Z {"@timestamp":"2025-08-08T21:21:12.977Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_transaction.1m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      218: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.006Z", "log.level": "INFO", "message":"adding component template [logs-apm.error@mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      219: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.008Z", "log.level": "INFO", "message":"adding component template [logs-apm.error-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      220: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.011Z", "log.level": "INFO", "message":"adding component template [metrics-apm.transaction.60m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      221: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.015Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_summary.60m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      222: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.019Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_transaction.10m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      223: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.023Z", "log.level": "INFO", "message":"adding component template [logs-apm@settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      224: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.025Z", "log.level": "INFO", "message":"adding component template [metrics-apm.service_destination.10m-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      225: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.028Z", "log.level": "INFO", "message":"adding component template [metrics-apm.app-fallback@ilm]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      226: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.031Z", "log.level": "INFO", "message":"adding component template [metrics-apm@settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      227: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.191Z", "log.level": "INFO", "message":"adding index template [elastic-connectors-sync-jobs] for index patterns [.elastic-connectors-sync-jobs-v1]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      228: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.200Z", "log.level": "INFO", "message":"adding index template [elastic-connectors] for index patterns [.elastic-connectors-v1]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      229: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.207Z", "log.level": "INFO", "message":"adding index template [synthetics] for index patterns [synthetics-*-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      230: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.243Z", "log.level": "INFO", "message":"adding index template [metrics-service_summary.60m.otel@template] for index patterns [metrics-service_summary.60m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      231: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.266Z", "log.level": "INFO", "message":"adding index template [metrics-transaction.60m.otel@template] for index patterns [metrics-transaction.60m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      232: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.281Z", "log.level": "INFO", "message":"adding index template [traces-otel@template] for index patterns [traces-*.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      233: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.307Z", "log.level": "INFO", "message":"adding index template [metrics-service_summary.1m.otel@template] for index patterns [metrics-service_summary.1m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      234: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.334Z", "log.level": "INFO", "message":"adding index template [metrics-service_transaction.1m.otel@template] for index patterns [metrics-service_transaction.1m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      235: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.360Z", "log.level": "INFO", "message":"adding index template [metrics-service_transaction.60m.otel@template] for index patterns [metrics-service_transaction.60m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      236: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.389Z", "log.level": "INFO", "message":"adding index template [metrics-service_transaction.10m.otel@template] for index patterns [metrics-service_transaction.10m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      237: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.395Z", "log.level": "INFO", "message":"adding index template [metrics] for index patterns [metrics-*-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      238: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.414Z", "log.level": "INFO", "message":"adding index template [metrics-transaction.10m.otel@template] for index patterns [metrics-transaction.10m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      239: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.429Z", "log.level": "INFO", "message":"adding index template [metrics-otel@template] for index patterns [metrics-*.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      240: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.449Z", "log.level": "INFO", "message":"adding index template [metrics-service_summary.10m.otel@template] for index patterns [metrics-service_summary.10m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      241: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.470Z", "log.level": "INFO", "message":"adding index template [metrics-service_destination.60m@template] for index patterns [metrics-service_destination.60m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      242: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.500Z", "log.level": "INFO", "message":"adding index template [metrics-service_destination.1m@template] for index patterns [metrics-service_destination.1m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      243: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.521Z", "log.level": "INFO", "message":"adding index template [metrics-service_destination.10m@template] for index patterns [metrics-service_destination.10m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      244: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.541Z", "log.level": "INFO", "message":"adding index template [metrics-transaction.1m.otel@template] for index patterns [metrics-transaction.1m.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      245: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.550Z", "log.level": "INFO", "message":"adding index template [.kibana-reporting] for index patterns [.kibana-reporting*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      246: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.561Z", "log.level": "INFO", "message":"adding index template [metrics-apm.service_transaction.1m@template] for index patterns [metrics-apm.service_transaction.1m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      247: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.575Z", "log.level": "INFO", "message":"adding index template [metrics-apm.service_summary.10m@template] for index patterns [metrics-apm.service_summary.10m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      248: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.587Z", "log.level": "INFO", "message":"adding index template [metrics-apm.internal@template] for index patterns [metrics-apm.internal-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      249: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.602Z", "log.level": "INFO", "message":"adding index template [metrics-apm.transaction.10m@template] for index patterns [metrics-apm.transaction.10m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      250: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.614Z", "log.level": "INFO", "message":"adding index template [logs-apm.error@template] for index patterns [logs-apm.error-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      251: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.627Z", "log.level": "INFO", "message":"adding index template [metrics-apm.transaction.1m@template] for index patterns [metrics-apm.transaction.1m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      252: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.637Z", "log.level": "INFO", "message":"adding index template [.deprecation-indexing-template] for index patterns [.logs-deprecation.*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      253: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.643Z", "log.level": "INFO", "message":"adding index template [metrics-apm.service_destination.60m@template] for index patterns [metrics-apm.service_destination.60m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      254: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.655Z", "log.level": "INFO", "message":"adding index template [metrics-apm.service_transaction.60m@template] for index patterns [metrics-apm.service_transaction.60m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      255: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.666Z", "log.level": "INFO", "message":"adding index template [metrics-apm.service_summary.1m@template] for index patterns [metrics-apm.service_summary.1m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      256: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.677Z", "log.level": "INFO", "message":"adding index template [metrics-apm.service_destination.1m@template] for index patterns [metrics-apm.service_destination.1m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      257: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.689Z", "log.level": "INFO", "message":"adding index template [metrics-apm.service_summary.60m@template] for index patterns [metrics-apm.service_summary.60m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      258: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.701Z", "log.level": "INFO", "message":"adding index template [traces-apm.rum@template] for index patterns [traces-apm.rum-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      259: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.709Z", "log.level": "INFO", "message":"adding index template [metrics-apm.app@template] for index patterns [metrics-apm.app.*-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      260: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.716Z", "log.level": "INFO", "message":"adding index template [metrics-apm.service_destination.10m@template] for index patterns [metrics-apm.service_destination.10m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      261: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.723Z", "log.level": "INFO", "message":"adding index template [metrics-apm.service_transaction.10m@template] for index patterns [metrics-apm.service_transaction.10m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      262: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.730Z", "log.level": "INFO", "message":"adding index template [traces-apm.sampled@template] for index patterns [traces-apm.sampled-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      263: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.737Z", "log.level": "INFO", "message":"adding index template [traces-apm@template] for index patterns [traces-apm-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      264: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.745Z", "log.level": "INFO", "message":"adding index template [metrics-apm.transaction.60m@template] for index patterns [metrics-apm.transaction.60m-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      265: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.755Z", "log.level": "INFO", "message":"adding index template [logs-apm.app@template] for index patterns [logs-apm.app.*-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      266: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.823Z", "log.level": "INFO", "message":"adding index lifecycle policy [.monitoring-8-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      267: 2025-08-08T21:21:13.0000000Z {"@timestamp":"2025-08-08T21:21:13.893Z", "log.level": "INFO", "message":"adding index lifecycle policy [ml-size-based-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      268: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.023Z", "log.level": "INFO", "message":"adding ingest pipeline behavioral_analytics-events-final_pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      269: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.023Z", "log.level": "INFO", "message":"adding ingest pipeline logs-default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      270: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.024Z", "log.level": "INFO", "message":"adding ingest pipeline logs@default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      271: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.024Z", "log.level": "INFO", "message":"adding ingest pipeline logs-apm.error@default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      272: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.024Z", "log.level": "INFO", "message":"adding ingest pipeline metrics-apm.service_transaction@default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      273: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.024Z", "log.level": "INFO", "message":"adding ingest pipeline metrics-apm.app@default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      274: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.025Z", "log.level": "INFO", "message":"adding ingest pipeline metrics-apm.service_destination@default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      275: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.025Z", "log.level": "INFO", "message":"adding ingest pipeline metrics-apm.service_summary@default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      276: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.025Z", "log.level": "INFO", "message":"adding ingest pipeline logs-apm.app@default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      277: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.025Z", "log.level": "INFO", "message":"adding ingest pipeline metrics-apm.transaction@default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      278: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.025Z", "log.level": "INFO", "message":"adding ingest pipeline traces-apm@default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      279: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.025Z", "log.level": "INFO", "message":"adding ingest pipeline apm@pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      280: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.026Z", "log.level": "INFO", "message":"adding ingest pipeline traces-apm.rum@default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      281: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.026Z", "log.level": "INFO", "message":"adding ingest pipeline logs@json-message", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      282: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.026Z", "log.level": "INFO", "message":"adding ingest pipeline logs@json-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      283: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.026Z", "log.level": "INFO", "message":"adding ingest pipeline ent-search-generic-ingestion", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      284: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.026Z", "log.level": "INFO", "message":"adding ingest pipeline search-default-ingestion", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      285: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.032Z", "log.level": "INFO", "message":"adding component template [behavioral_analytics-events-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      286: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.036Z", "log.level": "INFO", "message":"adding component template [logs@settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      287: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.039Z", "log.level": "INFO", "message":"adding component template [logs-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      288: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.107Z", "log.level": "INFO", "message":"adding index template [behavioral_analytics-events-default] for index patterns [behavioral_analytics-events-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      289: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.115Z", "log.level": "INFO", "message":"adding index template [logs] for index patterns [logs-*-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      290: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.125Z", "log.level": "INFO", "message":"adding index template [logs-otel@template] for index patterns [logs-*.otel-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      291: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.179Z", "log.level": "INFO", "message":"adding index lifecycle policy [synthetics]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      292: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.234Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      293: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.287Z", "log.level": "INFO", "message":"adding index lifecycle policy [30-days-default]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      294: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.345Z", "log.level": "INFO", "message":"adding index lifecycle policy [365-days-default]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      295: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.397Z", "log.level": "INFO", "message":"adding index lifecycle policy [90-days-default]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      296: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.444Z", "log.level": "INFO", "message":"adding index lifecycle policy [7-days-default]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      297: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.494Z", "log.level": "INFO", "message":"adding index lifecycle policy [180-days-default]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      298: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.542Z", "log.level": "INFO", "message":"adding index lifecycle policy [logs]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      299: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.585Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      300: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.631Z", "log.level": "INFO", "message":"adding index lifecycle policy [logs@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      301: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.679Z", "log.level": "INFO", "message":"adding index lifecycle policy [traces@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      302: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.719Z", "log.level": "INFO", "message":"adding index lifecycle policy [30-days@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      303: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.773Z", "log.level": "INFO", "message":"adding index lifecycle policy [180-days@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      304: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.843Z", "log.level": "INFO", "message":"adding index lifecycle policy [90-days@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      305: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.919Z", "log.level": "INFO", "message":"adding index lifecycle policy [7-days@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      306: 2025-08-08T21:21:14.0000000Z {"@timestamp":"2025-08-08T21:21:14.973Z", "log.level": "INFO", "message":"adding index lifecycle policy [synthetics@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      307: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.033Z", "log.level": "INFO", "message":"adding index lifecycle policy [365-days@lifecycle]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      308: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.099Z", "log.level": "INFO", "message":"adding index lifecycle policy [watch-history-ilm-policy-16]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      309: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.160Z", "log.level": "INFO", "message":"adding index lifecycle policy [ilm-history-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      310: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.214Z", "log.level": "INFO", "message":"adding index lifecycle policy [slm-history-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      311: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.273Z", "log.level": "INFO", "message":"adding index lifecycle policy [.deprecation-indexing-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      312: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.328Z", "log.level": "INFO", "message":"adding index lifecycle policy [.fleet-file-fromhost-meta-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      313: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.381Z", "log.level": "INFO", "message":"adding index lifecycle policy [.fleet-file-fromhost-data-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      314: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.441Z", "log.level": "INFO", "message":"adding index lifecycle policy [.fleet-actions-results-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      315: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.503Z", "log.level": "INFO", "message":"adding index lifecycle policy [.fleet-file-tohost-data-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      316: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.565Z", "log.level": "INFO", "message":"adding index lifecycle policy [.fleet-file-tohost-meta-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      317: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.669Z", "log.level": "INFO", "message":"adding index lifecycle policy [logs-apm.error_logs-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      318: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.721Z", "log.level": "INFO", "message":"adding index lifecycle policy [logs-apm.app_logs-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      319: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.777Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.internal_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      320: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.834Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.service_summary_10m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      321: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.903Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.service_transaction_10m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      322: 2025-08-08T21:21:15.0000000Z {"@timestamp":"2025-08-08T21:21:15.969Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.service_summary_60m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      323: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.025Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.service_destination_60m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      324: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.087Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.service_destination_1m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      325: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.154Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.app_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      326: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.221Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.service_destination_10m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      327: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.298Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.transaction_60m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      328: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.369Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.transaction_1m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      329: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.444Z", "log.level": "INFO", "message":"adding index lifecycle policy [traces-apm.rum_traces-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      330: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.516Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.service_transaction_1m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      331: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.589Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.service_transaction_60m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      332: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.666Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.transaction_10m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      333: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.733Z", "log.level": "INFO", "message":"adding index lifecycle policy [traces-apm.sampled_traces-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      1: 2025-08-08T21:48:06.0000000Z The files belonging to this database system will be owned by user "postgres".
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      2: 2025-08-08T21:48:06.0000000Z This user must also own the server process.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      3: 2025-08-08T21:48:06.0000000Z 
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      4: 2025-08-08T21:48:06.0000000Z The database cluster will be initialized with locale "en_US.utf8".
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      5: 2025-08-08T21:48:06.0000000Z The default database encoding has accordingly been set to "UTF8".
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      6: 2025-08-08T21:48:06.0000000Z The default text search configuration will be set to "english".
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      7: 2025-08-08T21:48:06.0000000Z 
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      8: 2025-08-08T21:48:06.0000000Z Data page checksums are disabled.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      9: 2025-08-08T21:48:06.0000000Z 
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      10: 2025-08-08T21:48:06.0000000Z fixing permissions on existing directory /var/lib/postgresql/data ... ok
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      11: 2025-08-08T21:48:06.0000000Z creating subdirectories ... ok
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      12: 2025-08-08T21:48:06.0000000Z selecting dynamic shared memory implementation ... posix
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      13: 2025-08-08T21:48:07.0000000Z selecting default "max_connections" ... 100
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      14: 2025-08-08T21:48:07.0000000Z selecting default "shared_buffers" ... 128MB
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      15: 2025-08-08T21:48:07.0000000Z selecting default time zone ... Etc/UTC
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      16: 2025-08-08T21:48:07.0000000Z creating configuration files ... ok
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      17: 2025-08-08T21:48:07.0000000Z running bootstrap script ... ok
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      18: 2025-08-08T21:48:07.0000000Z performing post-bootstrap initialization ... ok
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      19: 2025-08-08T21:48:08.0000000Z syncing data to disk ... ok
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      20: 2025-08-08T21:48:08.0000000Z 
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      21: 2025-08-08T21:48:08.0000000Z Success. You can now start the database server using:
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      22: 2025-08-08T21:48:08.0000000Z 
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      23: 2025-08-08T21:48:08.0000000Z     pg_ctl -D /var/lib/postgresql/data -l logfile start
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      24: 2025-08-08T21:48:08.0000000Z 
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      25: 2025-08-08T21:48:08.0000000Z waiting for server to start....2025-08-08 21:48:08.952 UTC [43] LOG:  starting PostgreSQL 17.5 (Debian 17.5-1.pgdg120+1) on x86_64-pc-linux-gnu, compiled by gcc (Debian 12.2.0-14) 12.2.0, 64-bit
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      26: 2025-08-08T21:48:08.0000000Z 2025-08-08 21:48:08.968 UTC [43] LOG:  listening on Unix socket "/var/run/postgresql/.s.PGSQL.5432"
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      27: 2025-08-08T21:48:09.0000000Z 2025-08-08 21:48:09.016 UTC [46] LOG:  database system was shut down at 2025-08-08 21:48:07 UTC
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      28: 2025-08-08T21:48:09.0000000Z 2025-08-08 21:48:09.045 UTC [43] LOG:  database system is ready to accept connections
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      29: 2025-08-08T21:48:09.0000000Z  done
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      30: 2025-08-08T21:48:09.0000000Z server started
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      31: 2025-08-08T21:48:10.0000000Z CREATE DATABASE
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      32: 2025-08-08T21:48:10.0000000Z 
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      33: 2025-08-08T21:48:10.0000000Z 
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      34: 2025-08-08T21:48:10.0000000Z /usr/local/bin/docker-entrypoint.sh: ignoring /docker-entrypoint-initdb.d/*
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      35: 2025-08-08T21:48:10.0000000Z 
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      36: 2025-08-08T21:48:10.0000000Z waiting for server to shut down...2025-08-08 21:48:10.180 UTC [43] LOG:  received fast shutdown request
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      37: 2025-08-08T21:48:10.0000000Z .2025-08-08 21:48:10.205 UTC [43] LOG:  aborting any active transactions
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      38: 2025-08-08T21:48:10.0000000Z 2025-08-08 21:48:10.207 UTC [43] LOG:  background worker "logical replication launcher" (PID 49) exited with exit code 1
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      39: 2025-08-08T21:48:10.0000000Z 2025-08-08 21:48:10.239 UTC [44] LOG:  shutting down
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      40: 2025-08-08T21:48:10.0000000Z 2025-08-08 21:48:10.275 UTC [44] LOG:  checkpoint starting: shutdown immediate
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      41: 2025-08-08T21:48:14.0000000Z ....2025-08-08 21:48:14.888 UTC [44] LOG:  checkpoint complete: wrote 921 buffers (5.6%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.534 s, sync=3.700 s, total=4.649 s; sync files=301, longest=0.540 s, average=0.013 s; distance=4238 kB, estimate=4238 kB; lsn=0/1908A18, redo lsn=0/1908A18
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      42: 2025-08-08T21:48:14.0000000Z 2025-08-08 21:48:14.895 UTC [43] LOG:  database system is shut down
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      43: 2025-08-08T21:48:14.0000000Z  done
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      44: 2025-08-08T21:48:14.0000000Z server stopped
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      45: 2025-08-08T21:48:14.0000000Z 
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      46: 2025-08-08T21:48:14.0000000Z PostgreSQL init process complete; ready for start up.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      47: 2025-08-08T21:48:14.0000000Z 
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      48: 2025-08-08T21:48:15.0000000Z 2025-08-08 21:48:15.036 UTC [1] LOG:  starting PostgreSQL 17.5 (Debian 17.5-1.pgdg120+1) on x86_64-pc-linux-gnu, compiled by gcc (Debian 12.2.0-14) 12.2.0, 64-bit
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      49: 2025-08-08T21:48:15.0000000Z 2025-08-08 21:48:15.037 UTC [1] LOG:  listening on IPv4 address "0.0.0.0", port 5432
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      50: 2025-08-08T21:48:15.0000000Z 2025-08-08 21:48:15.037 UTC [1] LOG:  listening on IPv6 address "::", port 5432
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      51: 2025-08-08T21:48:15.0000000Z 2025-08-08 21:48:15.052 UTC [1] LOG:  listening on Unix socket "/var/run/postgresql/.s.PGSQL.5432"
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      52: 2025-08-08T21:48:15.0000000Z 2025-08-08 21:48:15.070 UTC [59] LOG:  database system was shut down at 2025-08-08 21:48:14 UTC
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      53: 2025-08-08T21:48:15.0000000Z 2025-08-08 21:48:15.085 UTC [1] LOG:  database system is ready to accept connections
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      54: 2025-08-08T21:48:18.0000000Z 2025-08-08 21:48:18.618 UTC [63] FATAL:  database "wms" does not exist
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      55: 2025-08-08T21:50:44.0000000Z 2025-08-08 21:50:44.249 UTC [73] ERROR:  database "wms" already exists
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      56: 2025-08-08T21:50:44.0000000Z 2025-08-08 21:50:44.249 UTC [73] STATEMENT:  CREATE DATABASE "wms"
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      57: 2025-08-08T21:53:15.0000000Z 2025-08-08 21:53:15.171 UTC [57] LOG:  checkpoint starting: time
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      334: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.794Z", "log.level": "INFO", "message":"adding index lifecycle policy [traces-apm.traces-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      335: 2025-08-08T21:21:16.0000000Z {"@timestamp":"2025-08-08T21:21:16.864Z", "log.level": "INFO", "message":"adding index lifecycle policy [metrics-apm.service_summary_1m_metrics-default_policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.action.TransportPutLifecycleAction","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      336: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.028Z", "log.level": "INFO", "message":"Node [{ae8ed4e9b92a}{kgOIneAQRw-3dLYDiodD1g}] is selected as the current health node.", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][management][T#3]","log.logger":"org.elasticsearch.health.node.selection.HealthNodeTaskExecutor","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      58: 2025-08-08T21:55:25.0000000Z 2025-08-08 21:55:25.057 UTC [57] LOG:  checkpoint complete: wrote 1297 buffers (7.9%); 0 WAL file(s) added, 0 removed, 1 recycled; write=129.733 s, sync=0.109 s, total=129.887 s; sync files=336, longest=0.005 s, average=0.001 s; distance=10719 kB, estimate=10719 kB; lsn=0/2380790, redo lsn=0/2380700
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      337: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.126Z", "log.level": "INFO", "message":"[.ds-.logs-deprecation.elasticsearch-default-2025.08.08-000001] creating index, cause [initialize_data_stream], templates [.deprecation-indexing-template], shards [1]/[1]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataCreateIndexService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      338: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.168Z", "log.level": "INFO", "message":"adding data stream [.logs-deprecation.elasticsearch-default] with write index [.ds-.logs-deprecation.elasticsearch-default-2025.08.08-000001], backing indices [], and aliases []", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataCreateDataStreamService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      339: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.176Z", "log.level": "INFO", "message":"updating number_of_replicas to [0] for indices [.ds-.logs-deprecation.elasticsearch-default-2025.08.08-000001]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.routing.allocation.AllocationService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      340: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.186Z", "log.level": "INFO", "message":"license state changed, now [valid]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][generic][T#11]","log.logger":"org.elasticsearch.xpack.writeloadforecaster.LicensedWriteLoadForecaster","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      341: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.646Z", "log.level": "INFO", "message":"license mode is [basic], currently licensed security realms are [reserved/reserved,file/default_file,native/default_native]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][clusterApplierService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.security.authc.Realms","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      342: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.653Z", "log.level": "INFO", "message":"license [3d3fef28-e906-4550-aa73-7bb109b5283e] mode [basic] - valid", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][clusterApplierService#updateTask][T#1]","log.logger":"org.elasticsearch.license.ClusterStateLicenseService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      343: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.657Z", "log.level": "INFO", "message":"license state changed, now [not valid]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][generic][T#8]","log.logger":"org.elasticsearch.xpack.writeloadforecaster.LicensedWriteLoadForecaster","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      344: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.750Z", "log.level": "INFO", "message":"adding ingest pipeline metrics-apm.internal@default-pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      345: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.750Z", "log.level": "INFO", "message":"adding ingest pipeline traces-apm@pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      346: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.751Z", "log.level": "INFO", "message":"adding ingest pipeline metrics-apm@pipeline", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.core.template.IndexTemplateRegistry","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      347: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.755Z", "log.level": "INFO",  "current.health":"GREEN","message":"Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[.ds-.logs-deprecation.elasticsearch-default-2025.08.08-000001][0]]]).","previous.health":"YELLOW","reason":"shards started [[.ds-.logs-deprecation.elasticsearch-default-2025.08.08-000001][0]]" , "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.routing.allocation.AllocationService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      348: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.847Z", "log.level": "INFO", "message":"moving index [.ds-.logs-deprecation.elasticsearch-default-2025.08.08-000001] from [null] to [{\"phase\":\"new\",\"action\":\"complete\",\"name\":\"complete\"}] in policy [.deprecation-indexing-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.IndexLifecycleTransition","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      349: 2025-08-08T21:21:17.0000000Z {"@timestamp":"2025-08-08T21:21:17.951Z", "log.level": "INFO", "message":"[.ds-.logs-deprecation.elasticsearch-default-2025.08.08-000001/em-1jwjBQi-GUW6z8uwl7A] update_mapping [_doc]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataMappingService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      350: 2025-08-08T21:21:18.0000000Z {"@timestamp":"2025-08-08T21:21:18.032Z", "log.level": "INFO", "message":"moving index [.ds-.logs-deprecation.elasticsearch-default-2025.08.08-000001] from [{\"phase\":\"new\",\"action\":\"complete\",\"name\":\"complete\"}] to [{\"phase\":\"hot\",\"action\":\"unfollow\",\"name\":\"branch-check-unfollow-prerequisites\"}] in policy [.deprecation-indexing-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.IndexLifecycleTransition","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      351: 2025-08-08T21:21:18.0000000Z {"@timestamp":"2025-08-08T21:21:18.116Z", "log.level": "INFO", "message":"moving index [.ds-.logs-deprecation.elasticsearch-default-2025.08.08-000001] from [{\"phase\":\"hot\",\"action\":\"unfollow\",\"name\":\"branch-check-unfollow-prerequisites\"}] to [{\"phase\":\"hot\",\"action\":\"rollover\",\"name\":\"check-rollover-ready\"}] in policy [.deprecation-indexing-ilm-policy]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.xpack.ilm.IndexLifecycleTransition","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      352: 2025-08-08T21:21:22.0000000Z {"@timestamp":"2025-08-08T21:21:22.948Z", "log.level": "INFO", "message":"[.ds-ilm-history-7-2025.08.08-000001] creating index, cause [initialize_data_stream], templates [ilm-history-7], shards [1]/[1]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataCreateIndexService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      353: 2025-08-08T21:21:22.0000000Z {"@timestamp":"2025-08-08T21:21:22.949Z", "log.level": "INFO", "message":"adding data stream [ilm-history-7] with write index [.ds-ilm-history-7-2025.08.08-000001], backing indices [], and aliases []", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataCreateDataStreamService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      354: 2025-08-08T21:21:22.0000000Z {"@timestamp":"2025-08-08T21:21:22.951Z", "log.level": "INFO", "message":"updating number_of_replicas to [0] for indices [.ds-ilm-history-7-2025.08.08-000001]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.routing.allocation.AllocationService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-search[0]
      355: 2025-08-08T21:21:23.0000000Z {"@timestamp":"2025-08-08T21:21:23.208Z", "log.level": "INFO",  "current.health":"GREEN","message":"Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[.ds-ilm-history-7-2025.08.08-000001][0]]]).","previous.health":"YELLOW","reason":"shards started [[.ds-ilm-history-7-2025.08.08-000001][0]]" , "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[ae8ed4e9b92a][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.routing.allocation.AllocationService","elasticsearch.cluster.uuid":"fuoVlTYdSEmG3S3og8IRtw","elasticsearch.node.id":"kgOIneAQRw-3dLYDiodD1g","elasticsearch.node.name":"ae8ed4e9b92a","elasticsearch.cluster.name":"docker-cluster"}
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms' is ready.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-core-psql' is ready.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing ResourceReadyEvent for 'wms-core-psql'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing ResourceReadyEvent for 'wms'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Waiting for ResourceReadyEvent for 'wms'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      ResourceReadyEvent for 'wms' completed.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing the result of ResourceReadyEvent for 'wms'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-cdc[0]
      7: 2025-08-08T21:56:46.1636160Z Waiting for resource ready to execute for 'wms-core-psql'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-cdc[0]
      8: 2025-08-08T21:56:46.1639255Z Waiting for resource ready to execute for 'wms'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      5: 2025-08-08T21:56:46.1638903Z Waiting for resource ready to execute for 'wms'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      6: 2025-08-08T21:56:46.1639623Z Waiting for resource ready to execute for 'wms-core-psql'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      7: 2025-08-08T21:56:46.1646256Z Finished waiting for resource 'wms'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-cdc[0]
      9: 2025-08-08T21:56:46.1652334Z Finished waiting for resource 'wms'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      59: 2025-08-08T21:56:46.1683089Z Creating database 'wms'
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Waiting for ResourceReadyEvent for 'wms-core-psql'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      60: 2025-08-08T21:56:46.1825185Z Database 'wms' already exists
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      ResourceReadyEvent for 'wms-core-psql' completed.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing the result of ResourceReadyEvent for 'wms-core-psql'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-cdc[0]
      10: 2025-08-08T21:56:46.1858234Z Finished waiting for resource 'wms-core-psql'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      8: 2025-08-08T21:56:46.1858561Z Finished waiting for resource 'wms-core-psql'.
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-dataservice/wms-core-dataservice-wgmjzztq changed state: Running
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Starting health monitoring for resource 'wms-core-dataservice'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-core-dataservice' has no health checks to monitor.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-core-dataservice' is ready.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing ResourceReadyEvent for 'wms-core-dataservice'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Waiting for ResourceReadyEvent for 'wms-core-dataservice'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      ResourceReadyEvent for 'wms-core-dataservice' completed.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing the result of ResourceReadyEvent for 'wms-core-dataservice'.
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      61: 2025-08-08T21:56:46.0000000Z 2025-08-08 21:56:46.173 UTC [96] ERROR:  database "wms" already exists
fail: Miller.WMS.Edge.AppHost.Resources.wms-core-psql[0]
      62: 2025-08-08T21:56:46.0000000Z 2025-08-08 21:56:46.173 UTC [96] STATEMENT:  CREATE DATABASE "wms"
[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(6s)

info: Miller.WMS.Edge.AppHost.Resources.wms-edge-api[0]
      1: 2025-08-08T21:56:47.7250000Z info: Microsoft.Hosting.Lifetime[14]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-api[0]
      2: 2025-08-08T21:56:47.7260000Z       Now listening on: https://localhost:57788
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-api[0]
      3: 2025-08-08T21:56:47.7290000Z info: Microsoft.Hosting.Lifetime[14]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-api[0]
      4: 2025-08-08T21:56:47.7290000Z       Now listening on: http://localhost:57789
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-api[0]
      5: 2025-08-08T21:56:47.7310000Z info: Microsoft.Hosting.Lifetime[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-api[0]
      6: 2025-08-08T21:56:47.7310000Z       Application started. Press Ctrl+C to shut down.
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-api[0]
      7: 2025-08-08T21:56:47.7330000Z info: Microsoft.Hosting.Lifetime[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-api[0]
      8: 2025-08-08T21:56:47.7330000Z       Hosting environment: Development
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-api[0]
      9: 2025-08-08T21:56:47.7330000Z info: Microsoft.Hosting.Lifetime[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-api[0]
      10: 2025-08-08T21:56:47.7330000Z       Content root path: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.ApiService
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      9: 2025-08-08T21:56:48.6180000Z info: Miller.WMS.Core.DataService.Worker[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      10: 2025-08-08T21:56:48.6180000Z       Ensuring database is created and applying migrations...
info: Polly[3]
      Execution attempt. Source: '-standard//Standard-Retry', Operation Key: '', Result: '200', Handled: 'False', Attempt: '0', Execution Time: 4133.6275ms
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-edge-api' is ready.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing ResourceReadyEvent for 'wms-edge-api'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Waiting for ResourceReadyEvent for 'wms-edge-api'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      ResourceReadyEvent for 'wms-edge-api' completed.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing the result of ResourceReadyEvent for 'wms-edge-api'.
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      4: 2025-08-08T21:56:48.7372287Z Waiting for resource ready to execute for 'wms-edge-api'.
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      5: 2025-08-08T21:56:48.7373376Z Finished waiting for resource 'wms-edge-api'.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      11: 2025-08-08T21:56:49.3110000Z info: Microsoft.Hosting.Lifetime[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      12: 2025-08-08T21:56:49.3110000Z       Application started. Press Ctrl+C to shut down.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      13: 2025-08-08T21:56:49.3130000Z info: Microsoft.Hosting.Lifetime[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      14: 2025-08-08T21:56:49.3130000Z       Hosting environment: Development
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      15: 2025-08-08T21:56:49.3140000Z info: Microsoft.Hosting.Lifetime[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      16: 2025-08-08T21:56:49.3140000Z       Content root path: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Core.DataService
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      17: 2025-08-08T21:56:49.5250000Z info: Microsoft.EntityFrameworkCore.Database.Command[20101]
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      18: 2025-08-08T21:56:49.5250000Z       Executed DbCommand (50ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      19: 2025-08-08T21:56:49.5250000Z       SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      20: 2025-08-08T21:56:49.5250000Z FROM pg_class AS cls
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      21: 2025-08-08T21:56:49.5250000Z JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      22: 2025-08-08T21:56:49.5250000Z WHERE
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      23: 2025-08-08T21:56:49.5250000Z         cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      24: 2025-08-08T21:56:49.5250000Z         ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      25: 2025-08-08T21:56:49.5250000Z         -- Exclude tables which are members of PG extensions
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      26: 2025-08-08T21:56:49.5250000Z         NOT EXISTS (
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      27: 2025-08-08T21:56:49.5250000Z             SELECT 1 FROM pg_depend WHERE
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      28: 2025-08-08T21:56:49.5250000Z                 classid=(
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      29: 2025-08-08T21:56:49.5250000Z                     SELECT cls.oid
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      30: 2025-08-08T21:56:49.5250000Z                     FROM pg_class AS cls
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      31: 2025-08-08T21:56:49.5250000Z                              JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      32: 2025-08-08T21:56:49.5250000Z                     WHERE relname='pg_class' AND ns.nspname='pg_catalog'
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      33: 2025-08-08T21:56:49.5250000Z                 ) AND
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      34: 2025-08-08T21:56:49.5250000Z                 objid=cls.oid AND
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      35: 2025-08-08T21:56:49.5250000Z                 deptype IN ('e', 'x')
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      36: 2025-08-08T21:56:49.5250000Z         )
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      37: 2025-08-08T21:56:49.5300000Z info: Miller.WMS.Core.DataService.Worker[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      38: 2025-08-08T21:56:49.5300000Z       Database setup complete.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      39: 2025-08-08T21:56:49.7960000Z info: Microsoft.EntityFrameworkCore.Database.Command[20101]
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      40: 2025-08-08T21:56:49.7960000Z       Executed DbCommand (4ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      41: 2025-08-08T21:56:49.7960000Z       SELECT EXISTS (
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      42: 2025-08-08T21:56:49.7960000Z           SELECT 1
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      43: 2025-08-08T21:56:49.7960000Z           FROM "Organizations" AS o)
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      44: 2025-08-08T21:56:49.8120000Z info: Microsoft.Hosting.Lifetime[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-core-dataservice[0]
      45: 2025-08-08T21:56:49.8120000Z       Application is shutting down...
[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(9s)

dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-psql-dbgate/wms-core-psql-dbgate-renvfers changed state: Starting -> Running
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Starting health monitoring for resource 'wms-core-psql-dbgate'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-core-psql-dbgate' has no health checks to monitor.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-core-psql-dbgate' is ready.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing ResourceReadyEvent for 'wms-core-psql-dbgate'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Waiting for ResourceReadyEvent for 'wms-core-psql-dbgate'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      ResourceReadyEvent for 'wms-core-psql-dbgate' completed.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing the result of ResourceReadyEvent for 'wms-core-psql-dbgate'.
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-edge-cache/wms-edge-cache-vztreuaf changed state: Starting -> Running
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Starting health monitoring for resource 'wms-edge-cache'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-edge-cache' health checks to monitor: wms-edge-cache_check
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      6: 2025-08-08T21:56:50.9861617Z Waiting for resource 'wms-edge-cache' to become healthy.
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql-dbgate[0]
      2: 2025-08-08T21:56:50.0000000Z {"pid":1,"caller":"directories","level":30,"msg":"Creating directory /root/.dbgate/logs","time":1754690210656}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql-dbgate[0]
      3: 2025-08-08T21:56:50.0000000Z {"pid":1,"caller":"apiIndex","name":"main","level":30,"msg":"Starting API process version 6.1.4","time":1754690210677}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql-dbgate[0]
      4: 2025-08-08T21:56:50.0000000Z {"pid":1,"caller":"connections","name":"main","connections":[{"_id":"***","engine":"postgres@dbgate-plugin-postgres","server":"***","user":"***","password":"***","port":"5432","useDatabaseUrl":false,"defaultDatabase":null,"singleDatabase":false,"displayName":"***","databases":null,"useSeparateSchemas":false}],"level":30,"msg":"Using connections from ENV variables","time":1754690210984}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql-dbgate[0]
      5: 2025-08-08T21:56:51.0000000Z {"pid":1,"caller":"useController","name":"main","level":30,"msg":"Calling init controller for controller /connections","time":1754690211210}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql-dbgate[0]
      6: 2025-08-08T21:56:51.0000000Z {"pid":1,"caller":"useController","name":"main","level":30,"msg":"Calling init controller for controller /database-connections","time":1754690211214}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql-dbgate[0]
      7: 2025-08-08T21:56:51.0000000Z {"pid":1,"caller":"useController","name":"main","level":30,"msg":"Calling init controller for controller /scheduler","time":1754690211217}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql-dbgate[0]
      8: 2025-08-08T21:56:51.0000000Z {"pid":1,"caller":"directories","name":"main","level":30,"msg":"Creating directory /root/.dbgate/files","time":1754690211218}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql-dbgate[0]
      9: 2025-08-08T21:56:51.0000000Z {"pid":1,"caller":"directories","name":"main","level":30,"msg":"Creating directory /root/.dbgate/run","time":1754690211220}
info: Miller.WMS.Edge.AppHost.Resources.wms-core-psql-dbgate[0]
      10: 2025-08-08T21:56:51.0000000Z {"pid":1,"caller":"main","name":"main","level":30,"msg":"DbGate API listening on port 3000 (docker build)","time":1754690211220}
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-edge-cache' is ready.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing ResourceReadyEvent for 'wms-edge-cache'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Waiting for ResourceReadyEvent for 'wms-edge-cache'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      ResourceReadyEvent for 'wms-edge-cache' completed.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing the result of ResourceReadyEvent for 'wms-edge-cache'.
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      7: 2025-08-08T21:56:51.5870876Z Waiting for resource ready to execute for 'wms-edge-cache'.
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      8: 2025-08-08T21:56:51.5871278Z Finished waiting for resource 'wms-edge-cache'.
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-cache[0]
      2: 2025-08-08T21:56:50.0000000Z 2:C 08 Aug 2025 21:56:50.287 # WARNING Memory overcommit must be enabled! Without it, a background save or replication may fail under low memory condition. Being disabled, it can also cause failures without low memory condition, see https://github.com/jemalloc/jemalloc/issues/1328. To fix this issue add 'vm.overcommit_memory = 1' to /etc/sysctl.conf and then reboot or run the command 'sysctl vm.overcommit_memory=1' for this to take effect.
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-cache[0]
      3: 2025-08-08T21:56:50.0000000Z 2:C 08 Aug 2025 21:56:50.287 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-cache[0]
      4: 2025-08-08T21:56:50.0000000Z 2:C 08 Aug 2025 21:56:50.287 * Redis version=7.4.5, bits=64, commit=00000000, modified=0, pid=2, just started
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-cache[0]
      5: 2025-08-08T21:56:50.0000000Z 2:C 08 Aug 2025 21:56:50.287 * Configuration loaded
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-cache[0]
      6: 2025-08-08T21:56:50.0000000Z 2:M 08 Aug 2025 21:56:50.288 * monotonic clock: POSIX clock_gettime
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-cache[0]
      7: 2025-08-08T21:56:50.0000000Z 2:M 08 Aug 2025 21:56:50.290 * Running mode=standalone, port=6379.
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-cache[0]
      8: 2025-08-08T21:56:50.0000000Z 2:M 08 Aug 2025 21:56:50.290 * Server initialized
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-cache[0]
      9: 2025-08-08T21:56:50.0000000Z 2:M 08 Aug 2025 21:56:50.291 * Ready to accept connections tcp
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-edge-web/wms-edge-web-fynbpsas changed state: Running
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Starting health monitoring for resource 'wms-edge-web'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-edge-web' health checks to monitor: wms-edge-web_https_/health_200_check
[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(12s)

info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      9: 2025-08-08T21:56:53.5530000Z info: Microsoft.Hosting.Lifetime[14]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      10: 2025-08-08T21:56:53.5530000Z       Now listening on: https://localhost:57891
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      11: 2025-08-08T21:56:53.5560000Z info: Microsoft.Hosting.Lifetime[14]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      12: 2025-08-08T21:56:53.5560000Z       Now listening on: http://localhost:57892
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      13: 2025-08-08T21:56:53.5580000Z info: Microsoft.Hosting.Lifetime[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      14: 2025-08-08T21:56:53.5580000Z       Application started. Press Ctrl+C to shut down.
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      15: 2025-08-08T21:56:53.5580000Z info: Microsoft.Hosting.Lifetime[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      16: 2025-08-08T21:56:53.5580000Z       Hosting environment: Development
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      17: 2025-08-08T21:56:53.5590000Z info: Microsoft.Hosting.Lifetime[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      18: 2025-08-08T21:56:53.5590000Z       Content root path: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Web
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      19: 2025-08-08T21:56:54.0490000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      20: 2025-08-08T21:56:54.0490000Z       Connecting (sync) on .NET 9.0.7 (StackExchange.Redis: v2.8.41.44383)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      21: 2025-08-08T21:56:54.0630000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      22: 2025-08-08T21:56:54.0630000Z       localhost:57787,password=*****
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      23: 2025-08-08T21:56:54.0700000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      24: 2025-08-08T21:56:54.0700000Z       localhost:57787/Interactive: Connecting...
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      25: 2025-08-08T21:56:54.0760000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      26: 2025-08-08T21:56:54.0760000Z       localhost:57787: BeginConnectAsync
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      27: 2025-08-08T21:56:54.0900000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      28: 2025-08-08T21:56:54.0900000Z       1 unique nodes specified (with tiebreaker)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      29: 2025-08-08T21:56:54.0910000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      30: 2025-08-08T21:56:54.0910000Z       localhost:57787: OnConnectedAsync init (State=Connecting)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      31: 2025-08-08T21:56:54.0930000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      32: 2025-08-08T21:56:54.0930000Z       Allowing 1 endpoint(s) 00:00:05 to respond...
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      33: 2025-08-08T21:56:54.0950000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      34: 2025-08-08T21:56:54.0950000Z       Awaiting 1 available task completion(s) for 5000ms, IOCP: (Busy=0,Free=1000,Min=1,Max=1000), WORKER: (Busy=1,Free=32766,Min=14,Max=32767), POOL: (Threads=4,QueuedItems=0,CompletedItems=19,Timers=5)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      35: 2025-08-08T21:56:54.1080000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      36: 2025-08-08T21:56:54.1080000Z       localhost:57787/Interactive: Connected 
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      37: 2025-08-08T21:56:54.1110000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      38: 2025-08-08T21:56:54.1110000Z       localhost:57787: Server handshake
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      39: 2025-08-08T21:56:54.1160000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      40: 2025-08-08T21:56:54.1160000Z       localhost:57787: Authenticating (password)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      41: 2025-08-08T21:56:54.1480000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      42: 2025-08-08T21:56:54.1480000Z       localhost:57787: Setting client name: JBUSSE-0525(SE.Redis-v2.8.41.44383)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      43: 2025-08-08T21:56:54.1540000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      44: 2025-08-08T21:56:54.1540000Z       localhost:57787: Setting client lib/ver
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      45: 2025-08-08T21:56:54.1540000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      46: 2025-08-08T21:56:54.1540000Z       localhost:57787: Auto-configuring...
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      47: 2025-08-08T21:56:54.1540000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      48: 2025-08-08T21:56:54.1540000Z       localhost:57787: Requesting tie-break (Key="__Booksleeve_TieBreak")...
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      49: 2025-08-08T21:56:54.1540000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      50: 2025-08-08T21:56:54.1540000Z       localhost:57787: Sending critical tracer (handshake): ECHO
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      51: 2025-08-08T21:56:54.1540000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      52: 2025-08-08T21:56:54.1540000Z       localhost:57787: Flushing outbound buffer
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      53: 2025-08-08T21:56:54.1540000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      54: 2025-08-08T21:56:54.1540000Z       localhost:57787: OnEstablishingAsync complete
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      55: 2025-08-08T21:56:54.1540000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      56: 2025-08-08T21:56:54.1540000Z       localhost:57787: Starting read
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      57: 2025-08-08T21:56:54.1670000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      58: 2025-08-08T21:56:54.1670000Z       localhost:57787: Auto-configured (CLIENT) connection-id: 5
info: Polly[3]
      Execution attempt. Source: '-standard//Standard-Retry', Operation Key: '', Result: '200', Handled: 'False', Attempt: '0', Execution Time: 2458.7202ms
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Resource 'wms-edge-web' is ready.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing ResourceReadyEvent for 'wms-edge-web'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Waiting for ResourceReadyEvent for 'wms-edge-web'.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      ResourceReadyEvent for 'wms-edge-web' completed.
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Publishing the result of ResourceReadyEvent for 'wms-edge-web'.
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      59: 2025-08-08T21:56:54.1740000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      60: 2025-08-08T21:56:54.1740000Z       localhost:57787: Auto-configured (CONFIG) read-only replica: true
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      61: 2025-08-08T21:56:54.1740000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      62: 2025-08-08T21:56:54.1740000Z       localhost:57787: Auto-configured (CONFIG) databases: 16
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      63: 2025-08-08T21:56:54.1750000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      64: 2025-08-08T21:56:54.1750000Z       localhost:57787: Auto-configured (INFO) role: primary
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      65: 2025-08-08T21:56:54.1750000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      66: 2025-08-08T21:56:54.1750000Z       localhost:57787: Auto-configured (INFO) version: 7.4.5
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      67: 2025-08-08T21:56:54.1770000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      68: 2025-08-08T21:56:54.1770000Z       localhost:57787: Auto-configured (INFO) server-type: Standalone
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      69: 2025-08-08T21:56:54.1770000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      70: 2025-08-08T21:56:54.1770000Z       Response from localhost:57787/Interactive / GET __Booksleeve_TieBreak: (null)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      71: 2025-08-08T21:56:54.1780000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      72: 2025-08-08T21:56:54.1780000Z       Response from localhost:57787/Interactive / ECHO: BulkString: 16 bytes
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      73: 2025-08-08T21:56:54.1780000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      74: 2025-08-08T21:56:54.1780000Z       localhost:57787: OnConnectedAsync completed (From command: ECHO)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      75: 2025-08-08T21:56:54.1790000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      76: 2025-08-08T21:56:54.1790000Z       All 1 available tasks completed cleanly, IOCP: (Busy=0,Free=1000,Min=1,Max=1000), WORKER: (Busy=2,Free=32765,Min=14,Max=32767), POOL: (Threads=5,QueuedItems=0,CompletedItems=30,Timers=4)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      77: 2025-08-08T21:56:54.1790000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      78: 2025-08-08T21:56:54.1790000Z       Endpoint summary:
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      79: 2025-08-08T21:56:54.1800000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      80: 2025-08-08T21:56:54.1800000Z         localhost:57787: Endpoint is (Interactive: ConnectedEstablished, Subscription: ConnectedEstablished)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      81: 2025-08-08T21:56:54.1800000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      82: 2025-08-08T21:56:54.1800000Z       Task summary:
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      83: 2025-08-08T21:56:54.1800000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      84: 2025-08-08T21:56:54.1800000Z         localhost:57787: Returned with success as Standalone primary (Source: From command: ECHO)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      85: 2025-08-08T21:56:54.1810000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      86: 2025-08-08T21:56:54.1810000Z       Election summary:
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      87: 2025-08-08T21:56:54.1810000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      88: 2025-08-08T21:56:54.1810000Z         Election: localhost:57787 had no tiebreaker set
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      89: 2025-08-08T21:56:54.1810000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      90: 2025-08-08T21:56:54.1810000Z         Election: Single primary detected: localhost:57787
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      91: 2025-08-08T21:56:54.1810000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      92: 2025-08-08T21:56:54.1810000Z       localhost:57787: Clearing as RedundantPrimary
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      93: 2025-08-08T21:56:54.1820000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      94: 2025-08-08T21:56:54.1820000Z       Endpoint Summary:
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      95: 2025-08-08T21:56:54.1820000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      96: 2025-08-08T21:56:54.1820000Z         localhost:57787: Standalone v7.4.5, primary; 16 databases; keep-alive: 00:01:00; int: ConnectedEstablished; sub: ConnectedEstablished, 1 active
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      97: 2025-08-08T21:56:54.1820000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      98: 2025-08-08T21:56:54.1820000Z         localhost:57787: int ops=14, qu=0, qs=0, qc=0, wr=0, socks=1; sub ops=7, qu=0, qs=0, qc=0, wr=0, subs=1, socks=1
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      99: 2025-08-08T21:56:54.1900000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      100: 2025-08-08T21:56:54.1900000Z         localhost:57787: Circular op-count snapshot; int: 0+14=14 (1.40 ops/s; spans 10s); sub: 0+7=7 (0.70 ops/s; spans 10s)
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      101: 2025-08-08T21:56:54.1900000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      102: 2025-08-08T21:56:54.1900000Z       Sync timeouts: 0; async timeouts: 0; fire and forget: 0; last heartbeat: -1s ago
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      103: 2025-08-08T21:56:54.1910000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      104: 2025-08-08T21:56:54.1910000Z       Starting heartbeat...
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      105: 2025-08-08T21:56:54.1920000Z info: StackExchange.Redis.ConnectionMultiplexer[0]
info: Miller.WMS.Edge.AppHost.Resources.wms-edge-web[0]
      106: 2025-08-08T21:56:54.1920000Z       Total connect time: 142 ms
[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(15s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(18s)

dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-dataservice/wms-core-dataservice-wgmjzztq changed state: Running -> Finished
dbug: Aspire.Hosting.Health.ResourceHealthCheckService[0]
      Stopping health monitoring for resource 'wms-core-dataservice'.
[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(21s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(24s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(27s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(30s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(33s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(36s)

info: Polly[3]
      Execution attempt. Source: '-standard//Standard-Retry', Operation Key: '', Result: '200', Handled: 'False', Attempt: '0', Execution Time: 5.0115ms
[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(39s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(42s)

info: Polly[3]
      Execution attempt. Source: '-standard//Standard-Retry', Operation Key: '', Result: '200', Handled: 'False', Attempt: '0', Execution Time: 9.5078ms
[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(45s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(48s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(51s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(54s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(57s)

[+0/x0/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(1m 00s)

info: Aspire.Hosting.DistributedApplication[0]
      Distributed application started. Press Ctrl+C to shut down.
failed Miller.WMS.Edge.Tests.ApiTests.GetApiResourceRootReturnsOkStatusCode (0ms)
  Xunit.Runner.InProc.SystemConsole.TestingPlatform.XunitException: Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
  ---- System.TimeoutException : The operation has timed out.
    
    ----- Inner Stack Trace -----
    at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync() in C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs:44
dbug: Aspire.Hosting.ApplicationModel.ResourceNotificationService[0]
      Resource wms-core-cdc/wms-core-cdc-nxdcfbmg changed state: Waiting -> FailedToStart
failed Miller.WMS.Edge.Tests.ApiTests.GetPingEndpointReturnsTrue (0ms)
  Xunit.Runner.InProc.SystemConsole.TestingPlatform.XunitException: Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
  ---- System.TimeoutException : The operation has timed out.
    
    ----- Inner Stack Trace -----
    at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync() in C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs:44
failed Miller.WMS.Edge.Tests.CdcWorkerTests.CdcWorker_Ping_ReturnsTrue (0ms)
  Xunit.Runner.InProc.SystemConsole.TestingPlatform.XunitException: Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
  ---- System.TimeoutException : The operation has timed out.
    
    ----- Inner Stack Trace -----
    at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync() in C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs:44
failed Miller.WMS.Edge.Tests.DataTests.DataServiceCompletedSuccessfully (0ms)
  Xunit.Runner.InProc.SystemConsole.TestingPlatform.XunitException: Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
  ---- System.TimeoutException : The operation has timed out.
    
    ----- Inner Stack Trace -----
    at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync() in C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs:44
failed Miller.WMS.Edge.Tests.DataTests.DatabaseServiceHealthy (0ms)
  Xunit.Runner.InProc.SystemConsole.TestingPlatform.XunitException: Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
  ---- System.TimeoutException : The operation has timed out.
    
    ----- Inner Stack Trace -----
    at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync() in C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs:44
failed Miller.WMS.Edge.Tests.DataTests.AllServicesHealthy (0ms)
  Xunit.Runner.InProc.SystemConsole.TestingPlatform.XunitException: Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
  ---- System.TimeoutException : The operation has timed out.
    
    ----- Inner Stack Trace -----
    at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync() in C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs:44
failed Miller.WMS.Edge.Tests.DataTests.DataServiceLogsIndicateSuccess (0ms)
  Xunit.Runner.InProc.SystemConsole.TestingPlatform.XunitException: Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
  ---- System.TimeoutException : The operation has timed out.
    
    ----- Inner Stack Trace -----
    at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync() in C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs:44
failed Miller.WMS.Edge.Tests.WebTests.GetWebResourceRootReturnsOkStatusCode (0ms)
  Xunit.Runner.InProc.SystemConsole.TestingPlatform.XunitException: Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
  ---- System.TimeoutException : The operation has timed out.
    
    ----- Inner Stack Trace -----
    at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync() in C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs:44
fail: Aspire.Hosting.Dcp.dcp.start-apiserver.api-server.container-logstreamer.LogDescriptorSet[0]
      Error disposing log descriptor	{"ResourceName": {"name":"wms-core-search-ceba9998"}, "ResourceUID": "1fa0c360-0b17-4237-a353-ff0a7c6cbd8d", "error": "context deadline exceeded\ncontext deadline exceeded"}
fail: Aspire.Hosting.Dcp.dcp.start-apiserver.api-server.container-logstreamer.LogDescriptorSet[0]
      Error disposing log descriptor	{"ResourceName": {"name":"wms-core-psql-ceba9998"}, "ResourceUID": "792b4373-88b5-40c0-a555-04e18ab0fa86", "error": "context deadline exceeded\ncontext deadline exceeded"}
[+0/x8/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(1m 03s)

[+0/x8/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(1m 06s)

[+0/x8/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(1m 09s)

[+0/x8/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(1m 12s)

[+0/x8/?0] Miller.WMS.Edge.Tests.dll (net9.0|x64)(1m 15s)

fail: Aspire.Hosting.Dcp.dcpctrl.controller-runtime.cache.UnhandledError[0]
      Failed to watch	{"reflector": "pkg/mod/k8s.io/client-go@v0.33.0/tools/cache/reflector.go:285", "type": "*v1.ContainerVolume", "error": "Get \"https://127.0.0.1:57766/apis/usvc-dev.developer.microsoft.com/v1/containervolumes?allowWatchBookmarks=true&resourceVersion=1&timeoutSeconds=484&watch=true\": dial tcp 127.0.0.1:57766: connectex: No connection could be made because the target machine actively refused it."}
fail: Aspire.Hosting.Dcp.dcpctrl.controller-runtime.cache.UnhandledError[0]
      Failed to watch	{"reflector": "pkg/mod/k8s.io/client-go@v0.33.0/tools/cache/reflector.go:285", "type": "*v1.Executable", "error": "Get \"https://127.0.0.1:57766/apis/usvc-dev.developer.microsoft.com/v1/executables?allowWatchBookmarks=true&resourceVersion=137&timeoutSeconds=431&watch=true\": dial tcp 127.0.0.1:57766: connectex: No connection could be made because the target machine actively refused it."}
fail: Aspire.Hosting.Dcp.dcpctrl.controller-runtime.cache.UnhandledError[0]
      Failed to watch	{"reflector": "pkg/mod/k8s.io/client-go@v0.33.0/tools/cache/reflector.go:285", "type": "*v1.ContainerNetwork", "error": "Get \"https://127.0.0.1:57766/apis/usvc-dev.developer.microsoft.com/v1/containernetworks?allowWatchBookmarks=true&resourceVersion=140&timeoutSeconds=471&watch=true\": dial tcp 127.0.0.1:57766: connectex: No connection could be made because the target machine actively refused it."}
fail: Aspire.Hosting.Dcp.dcpctrl.controller-runtime.cache.UnhandledError[0]
      Failed to watch	{"reflector": "pkg/mod/k8s.io/client-go@v0.33.0/tools/cache/reflector.go:285", "type": "*v1.ExecutableReplicaSet", "error": "Get \"https://127.0.0.1:57766/apis/usvc-dev.developer.microsoft.com/v1/executablereplicasets?allowWatchBookmarks=true&resourceVersion=1&timeoutSeconds=446&watch=true\": dial tcp 127.0.0.1:57766: connectex: No connection could be made because the target machine actively refused it."}
fail: Aspire.Hosting.Dcp.dcpctrl.controller-runtime.cache.UnhandledError[0]
      Failed to watch	{"reflector": "pkg/mod/k8s.io/client-go@v0.33.0/tools/cache/reflector.go:285", "type": "*v1.ContainerExec", "error": "Get \"https://127.0.0.1:57766/apis/usvc-dev.developer.microsoft.com/v1/containerexecs?allowWatchBookmarks=true&resourceVersion=1&timeoutSeconds=382&watch=true\": dial tcp 127.0.0.1:57766: connectex: No connection could be made because the target machine actively refused it."}
fail: Aspire.Hosting.Dcp.dcpctrl.controller-runtime.cache.UnhandledError[0]
      Failed to watch	{"reflector": "pkg/mod/k8s.io/client-go@v0.33.0/tools/cache/reflector.go:285", "type": "*v1.Container", "error": "Get \"https://127.0.0.1:57766/apis/usvc-dev.developer.microsoft.com/v1/containers?allowWatchBookmarks=true&resourceVersion=138&timeoutSeconds=559&watch=true\": dial tcp 127.0.0.1:57766: connectex: No connection could be made because the target machine actively refused it."}
fail: Aspire.Hosting.Dcp.dcpctrl.controller-runtime.cache.UnhandledError[0]
      Failed to watch	{"reflector": "pkg/mod/k8s.io/client-go@v0.33.0/tools/cache/reflector.go:285", "type": "*v1.Endpoint", "error": "Get \"https://127.0.0.1:57766/apis/usvc-dev.developer.microsoft.com/v1/endpoints?allowWatchBookmarks=true&resourceVersion=53&timeoutSeconds=461&watch=true\": dial tcp 127.0.0.1:57766: connectex: No connection could be made because the target machine actively refused it."}
fail: Aspire.Hosting.Dcp.dcpctrl.controller-runtime.cache.UnhandledError[0]
      Failed to watch	{"reflector": "pkg/mod/k8s.io/client-go@v0.33.0/tools/cache/reflector.go:285", "type": "*v1.Service", "error": "Get \"https://127.0.0.1:57766/apis/usvc-dev.developer.microsoft.com/v1/services?allowWatchBookmarks=true&resourceVersion=118&timeoutSeconds=364&watch=true\": dial tcp 127.0.0.1:57766: connectex: No connection could be made because the target machine actively refused it."}
fail: Aspire.Hosting.Dcp.dcpctrl.controller-runtime.cache.UnhandledError[0]
      Failed to watch	{"reflector": "pkg/mod/k8s.io/client-go@v0.33.0/tools/cache/reflector.go:285", "type": "*v1.ContainerNetworkConnection", "error": "Get \"https://127.0.0.1:57766/apis/usvc-dev.developer.microsoft.com/v1/containernetworkconnections?allowWatchBookmarks=true&resourceVersion=75&timeoutSeconds=472&watch=true\": dial tcp 127.0.0.1:57766: connectex: No connection could be made because the target machine actively refused it."}

Test run summary: Failed! - C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Miller.WMS.Edge.Tests.dll (net9.0|x64)
  total: 8
  failed: 8
  succeeded: 0
  skipped: 0
  duration: 1m 17s 156ms

=== COMMAND LINE ===
C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Miller.WMS.Edge.Tests.exe --internal-msbuild-node testingplatform.pipe.9d8275b1a6e4445d987c07873f5c3bf3 
