[{"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_0", "RelativePath": "ping", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain"], "StatusCode": 200}], "EndpointName": "<PERSON>"}, {"ContainingType": "Program+<>c__DisplayClass0_0", "Method": "<<Main>$>b__1", "RelativePath": "weatherforecast", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WeatherForecast[]", "MediaTypes": ["application/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]