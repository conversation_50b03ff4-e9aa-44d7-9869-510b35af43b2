2025-08-06T12:53:57.9112435+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Version: 1.6.2+88f8ce447cd12b629fcfe5e61d80dcc0c8cab8ec
2025-08-06T12:53:57.9280827+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging mode: asynchronous
2025-08-06T12:53:57.9299834+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging level: Information
2025-08-06T12:53:57.9299995+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION CreateBuilderAsync entry time: 12:53:57.855
2025-08-06T12:53:57.9379633+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION PID: 35180
2025-08-06T12:53:57.9380102+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime information: win-x64 - .NET 9.0.7
2025-08-06T12:53:57.9380257+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime location: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.7\System.Private.CoreLib.dll
2025-08-06T12:53:57.9380414+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION IsDynamicCodeSupported: True
2025-08-06T12:53:57.9380460+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Test module: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Miller.WMS.Edge.Tests.dll
2025-08-06T12:53:57.9381755+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Command line arguments: '--server --diagnostic --diagnostic-verbosity Information --diagnostic-output-directory C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Log --results-directory C:\_\Miller_Github\Miller.WMS\TestResults --client-port 58615'
2025-08-06T12:53:57.9409146+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION TESTINGPLATFORM_DEFAULT_HANG_TIMEOUT: ''
2025-08-06T12:53:57.9626103+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting RegisterEnvironmentVariablesConfigurationSource: 'True'
2025-08-06T12:53:57.9867036+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting PlatformExitProcessOnUnhandledException: 'False', config file: False environment variable:  VSTest mode: False
2025-08-06T12:53:58.3743363+00:00 Microsoft.Testing.Platform.Requests.TestHostTestFrameworkInvoker INFORMATION Test framework UID: '30ea7c6e-dd24-4152-a360-1387158cd41d' Version: '2.0.0' DisplayName: 'xUnit.net v3 Microsoft.Testing.Platform test framework' Description: 'xUnit.net v3 Microsoft.Testing.Platform test framework'
2025-08-06T12:53:58.3848867+00:00 xUnit.net INFORMATION xUnit.net v3 In-Process Runner v2.0.0+229879b765 (64-bit .NET 9.0.7)
2025-08-06T12:53:58.4444229+00:00 xUnit.net INFORMATION   Discovering: Miller.WMS.Edge.Tests
2025-08-06T12:53:58.4990462+00:00 xUnit.net INFORMATION   Discovered:  Miller.WMS.Edge.Tests
2025-08-06T12:53:58.5242935+00:00 xUnit.net INFORMATION   Starting:    Miller.WMS.Edge.Tests
2025-08-06T12:54:35.6690709+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CDCTests.CDCServiceStartsSuccessfully [FAIL]
2025-08-06T12:54:35.6704819+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T12:54:35.6705224+00:00 xUnit.net INFORMATION       ---- Aspire.Hosting.DistributedApplicationException : Container runtime 'docker' was found but appears to be unhealthy. Ensure that Docker is running and that the Docker daemon is accessible. If Resource Saver mode is enabled, containers may not run. For more information, visit: https://docs.docker.com/desktop/use-desktop/resource-saver/
2025-08-06T12:54:35.6711467+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T12:54:35.6715516+00:00 xUnit.net INFORMATION         
2025-08-06T12:54:35.6716036+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T12:54:35.6716528+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpDependencyCheck.cs(254,0): at Aspire.Hosting.Dcp.DcpDependencyCheck.CheckDcpInfoAndLogErrors(ILogger logger, DcpOptions options, DcpInfo dcpInfo, Boolean throwIfUnhealthy)
2025-08-06T12:54:35.6716696+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpHost.cs(112,0): at Aspire.Hosting.Dcp.DcpHost.EnsureDcpContainerRuntimeAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6716822+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpHost.cs(57,0): at Aspire.Hosting.Dcp.DcpHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6716972+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Orchestrator/OrchestratorHostService.cs(39,0): at Aspire.Hosting.Orchestrator.OrchestratorHostService.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6717116+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
2025-08-06T12:54:35.6717298+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-08-06T12:54:35.6717399+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6717533+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(616,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.ObservedHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6717848+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(74,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6718007+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(284,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6718152+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(259,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedDistributedApplication.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6718312+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T12:54:35.6776767+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CDCTests.ElasticsearchServiceIsHealthy [FAIL]
2025-08-06T12:54:35.6777124+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T12:54:35.6777169+00:00 xUnit.net INFORMATION       ---- Aspire.Hosting.DistributedApplicationException : Container runtime 'docker' was found but appears to be unhealthy. Ensure that Docker is running and that the Docker daemon is accessible. If Resource Saver mode is enabled, containers may not run. For more information, visit: https://docs.docker.com/desktop/use-desktop/resource-saver/
2025-08-06T12:54:35.6777499+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T12:54:35.6777588+00:00 xUnit.net INFORMATION         
2025-08-06T12:54:35.6777619+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T12:54:35.6777733+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpDependencyCheck.cs(254,0): at Aspire.Hosting.Dcp.DcpDependencyCheck.CheckDcpInfoAndLogErrors(ILogger logger, DcpOptions options, DcpInfo dcpInfo, Boolean throwIfUnhealthy)
2025-08-06T12:54:35.6777806+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpHost.cs(112,0): at Aspire.Hosting.Dcp.DcpHost.EnsureDcpContainerRuntimeAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6777895+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpHost.cs(57,0): at Aspire.Hosting.Dcp.DcpHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6777970+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Orchestrator/OrchestratorHostService.cs(39,0): at Aspire.Hosting.Orchestrator.OrchestratorHostService.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6778060+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
2025-08-06T12:54:35.6778277+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-08-06T12:54:35.6778434+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6778518+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(616,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.ObservedHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6778587+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(74,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6778698+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(284,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6778773+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(259,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedDistributedApplication.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6778864+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T12:54:35.6783152+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CDCTests.InitialSyncCompletedSuccessfully [FAIL]
2025-08-06T12:54:35.6783302+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T12:54:35.6783365+00:00 xUnit.net INFORMATION       ---- Aspire.Hosting.DistributedApplicationException : Container runtime 'docker' was found but appears to be unhealthy. Ensure that Docker is running and that the Docker daemon is accessible. If Resource Saver mode is enabled, containers may not run. For more information, visit: https://docs.docker.com/desktop/use-desktop/resource-saver/
2025-08-06T12:54:35.6783683+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T12:54:35.6783812+00:00 xUnit.net INFORMATION         
2025-08-06T12:54:35.6783851+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T12:54:35.6783981+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpDependencyCheck.cs(254,0): at Aspire.Hosting.Dcp.DcpDependencyCheck.CheckDcpInfoAndLogErrors(ILogger logger, DcpOptions options, DcpInfo dcpInfo, Boolean throwIfUnhealthy)
2025-08-06T12:54:35.6784088+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpHost.cs(112,0): at Aspire.Hosting.Dcp.DcpHost.EnsureDcpContainerRuntimeAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6784194+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpHost.cs(57,0): at Aspire.Hosting.Dcp.DcpHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6785226+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Orchestrator/OrchestratorHostService.cs(39,0): at Aspire.Hosting.Orchestrator.OrchestratorHostService.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6785415+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
2025-08-06T12:54:35.6785553+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-08-06T12:54:35.6785625+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6785742+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(616,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.ObservedHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6785814+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(74,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6785908+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(284,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6786009+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(259,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedDistributedApplication.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6786100+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T12:54:35.6787646+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CDCTests.OrganizationDataSyncedCorrectly [FAIL]
2025-08-06T12:54:35.6787700+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T12:54:35.6787716+00:00 xUnit.net INFORMATION       ---- Aspire.Hosting.DistributedApplicationException : Container runtime 'docker' was found but appears to be unhealthy. Ensure that Docker is running and that the Docker daemon is accessible. If Resource Saver mode is enabled, containers may not run. For more information, visit: https://docs.docker.com/desktop/use-desktop/resource-saver/
2025-08-06T12:54:35.6787861+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T12:54:35.6787935+00:00 xUnit.net INFORMATION         
2025-08-06T12:54:35.6787953+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T12:54:35.6788035+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpDependencyCheck.cs(254,0): at Aspire.Hosting.Dcp.DcpDependencyCheck.CheckDcpInfoAndLogErrors(ILogger logger, DcpOptions options, DcpInfo dcpInfo, Boolean throwIfUnhealthy)
2025-08-06T12:54:35.6788106+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpHost.cs(112,0): at Aspire.Hosting.Dcp.DcpHost.EnsureDcpContainerRuntimeAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6788176+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpHost.cs(57,0): at Aspire.Hosting.Dcp.DcpHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6788256+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Orchestrator/OrchestratorHostService.cs(39,0): at Aspire.Hosting.Orchestrator.OrchestratorHostService.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6788319+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
2025-08-06T12:54:35.6788545+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-08-06T12:54:35.6788605+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6788673+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(616,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.ObservedHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6788749+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(74,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6788829+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(284,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6788920+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(259,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedDistributedApplication.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6789012+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T12:54:35.6790295+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CDCTests.DatabaseChangesAreSyncedToElasticsearch [FAIL]
2025-08-06T12:54:35.6790364+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T12:54:35.6790380+00:00 xUnit.net INFORMATION       ---- Aspire.Hosting.DistributedApplicationException : Container runtime 'docker' was found but appears to be unhealthy. Ensure that Docker is running and that the Docker daemon is accessible. If Resource Saver mode is enabled, containers may not run. For more information, visit: https://docs.docker.com/desktop/use-desktop/resource-saver/
2025-08-06T12:54:35.6790544+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T12:54:35.6790607+00:00 xUnit.net INFORMATION         
2025-08-06T12:54:35.6790624+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T12:54:35.6790710+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpDependencyCheck.cs(254,0): at Aspire.Hosting.Dcp.DcpDependencyCheck.CheckDcpInfoAndLogErrors(ILogger logger, DcpOptions options, DcpInfo dcpInfo, Boolean throwIfUnhealthy)
2025-08-06T12:54:35.6790783+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpHost.cs(112,0): at Aspire.Hosting.Dcp.DcpHost.EnsureDcpContainerRuntimeAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6790848+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Dcp/DcpHost.cs(57,0): at Aspire.Hosting.Dcp.DcpHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6790943+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting/Orchestrator/OrchestratorHostService.cs(39,0): at Aspire.Hosting.Orchestrator.OrchestratorHostService.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6791030+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
2025-08-06T12:54:35.6791164+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-08-06T12:54:35.6791215+00:00 xUnit.net INFORMATION            at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6791270+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(616,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.ObservedHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6791316+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationFactory.cs(74,0): at Aspire.Hosting.Testing.DistributedApplicationFactory.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6791406+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(284,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedHost.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6791486+00:00 xUnit.net INFORMATION         /_/src/Aspire.Hosting.Testing/DistributedApplicationTestingBuilder.cs(259,0): at Aspire.Hosting.Testing.DistributedApplicationTestingBuilder.SuspendingDistributedApplicationFactory.DelegatedDistributedApplication.StartAsync(CancellationToken cancellationToken)
2025-08-06T12:54:35.6791569+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T12:54:35.7474161+00:00 xUnit.net INFORMATION   Finished:    Miller.WMS.Edge.Tests
2025-08-06T12:54:35.7546050+00:00 xUnit.net INFORMATION === TEST EXECUTION SUMMARY ===
2025-08-06T12:54:35.7626859+00:00 xUnit.net INFORMATION    Miller.WMS.Edge.Tests  Total: 5, Errors: 0, Failed: 5, Skipped: 0, Not Run: 0, Time: 37.232s
