2025-08-05T23:50:52.1172821+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Version: 1.6.2+88f8ce447cd12b629fcfe5e61d80dcc0c8cab8ec
2025-08-05T23:50:52.1302979+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging mode: asynchronous
2025-08-05T23:50:52.1320092+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging level: Information
2025-08-05T23:50:52.1320187+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION CreateBuilderAsync entry time: 23:50:52.091
2025-08-05T23:50:52.1369712+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION PID: 32540
2025-08-05T23:50:52.1370300+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime information: win-x64 - .NET 9.0.7
2025-08-05T23:50:52.1370447+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime location: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.7\System.Private.CoreLib.dll
2025-08-05T23:50:52.1370572+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION IsDynamicCodeSupported: True
2025-08-05T23:50:52.1370613+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Test module: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Miller.WMS.Edge.Tests.dll
2025-08-05T23:50:52.1371345+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Command line arguments: '--server --diagnostic --diagnostic-verbosity Information --diagnostic-output-directory C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Log --results-directory C:\_\Miller_Github\Miller.WMS\TestResults --client-port 62087'
2025-08-05T23:50:52.1383783+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION TESTINGPLATFORM_DEFAULT_HANG_TIMEOUT: ''
2025-08-05T23:50:52.1525838+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting RegisterEnvironmentVariablesConfigurationSource: 'True'
2025-08-05T23:50:52.1657683+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting PlatformExitProcessOnUnhandledException: 'False', config file: False environment variable:  VSTest mode: False
2025-08-05T23:50:52.3178459+00:00 Microsoft.Testing.Platform.Requests.TestHostTestFrameworkInvoker INFORMATION Test framework UID: '30ea7c6e-dd24-4152-a360-1387158cd41d' Version: '2.0.0' DisplayName: 'xUnit.net v3 Microsoft.Testing.Platform test framework' Description: 'xUnit.net v3 Microsoft.Testing.Platform test framework'
2025-08-05T23:50:52.3224482+00:00 xUnit.net INFORMATION xUnit.net v3 In-Process Runner v2.0.0+229879b765 (64-bit .NET 9.0.7)
2025-08-05T23:50:52.3477403+00:00 xUnit.net INFORMATION   Discovering: Miller.WMS.Edge.Tests
2025-08-05T23:50:52.3730929+00:00 xUnit.net INFORMATION   Discovered:  Miller.WMS.Edge.Tests
2025-08-05T23:50:52.3887497+00:00 xUnit.net INFORMATION   Starting:    Miller.WMS.Edge.Tests
2025-08-05T23:51:14.1554550+00:00 xUnit.net INFORMATION   Finished:    Miller.WMS.Edge.Tests
2025-08-05T23:51:14.1593982+00:00 xUnit.net INFORMATION === TEST EXECUTION SUMMARY ===
2025-08-05T23:51:14.1647687+00:00 xUnit.net INFORMATION    Miller.WMS.Edge.Tests  Total: 3, Errors: 0, Failed: 0, Skipped: 0, Not Run: 0, Time: 21.771s
