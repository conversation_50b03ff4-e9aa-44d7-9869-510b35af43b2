2025-08-06T22:41:15.0944240+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Version: 1.7.3+5abdae1f2e07071c4e81b27ac262f241708ec3cf
2025-08-06T22:41:15.1083286+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging mode: asynchronous
2025-08-06T22:41:15.1099900+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging level: Information
2025-08-06T22:41:15.1100005+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION CreateBuilderAsync entry time: 22:41:15.069
2025-08-06T22:41:15.1157622+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION PID: 740
2025-08-06T22:41:15.1158285+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime information: win-x64 - .NET 9.0.7
2025-08-06T22:41:15.1158452+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime location: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.7\System.Private.CoreLib.dll
2025-08-06T22:41:15.1159623+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION IsDynamicCodeSupported: True
2025-08-06T22:41:15.1160526+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Test module: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Miller.WMS.Edge.Tests.dll
2025-08-06T22:41:15.1161873+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Command line arguments: '--server --diagnostic --diagnostic-verbosity Information --diagnostic-output-directory C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Log --results-directory C:\_\Miller_Github\Miller.WMS\TestResults --client-port 62009'
2025-08-06T22:41:15.1183813+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION TESTINGPLATFORM_DEFAULT_HANG_TIMEOUT: ''
2025-08-06T22:41:15.1349918+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting RegisterEnvironmentVariablesConfigurationSource: 'True'
2025-08-06T22:41:15.1573102+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting PlatformExitProcessOnUnhandledException: 'False', config file: False environment variable: 
2025-08-06T22:41:15.4279813+00:00 Microsoft.Testing.Platform.Requests.TestHostTestFrameworkInvoker INFORMATION Test framework UID: '30ea7c6e-dd24-4152-a360-1387158cd41d' Version: '3.0.0' DisplayName: 'xUnit.net v3 Microsoft.Testing.Platform test framework' Description: 'xUnit.net v3 Microsoft.Testing.Platform test framework'
2025-08-06T22:41:15.4323515+00:00 xUnit.net INFORMATION xUnit.net v3 In-Process Runner v3.0.0+d0213fc4e5 (64-bit .NET 9.0.7)
2025-08-06T22:41:15.4826236+00:00 xUnit.net INFORMATION   Discovering: Miller.WMS.Edge.Tests
2025-08-06T22:41:15.5312609+00:00 xUnit.net INFORMATION   Discovered:  Miller.WMS.Edge.Tests
2025-08-06T22:41:15.5512367+00:00 xUnit.net INFORMATION   Starting:    Miller.WMS.Edge.Tests
2025-08-06T22:42:16.0051594+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.ApiTests.GetApiResourceRootReturnsOkStatusCode [FAIL]
2025-08-06T22:42:16.0058869+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T22:42:16.0058974+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T22:42:16.0063339+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T22:42:16.0079324+00:00 xUnit.net INFORMATION         
2025-08-06T22:42:16.0079518+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T22:42:16.0079805+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T22:42:16.0176646+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.ApiTests.GetPingEndpointReturnsTrue [FAIL]
2025-08-06T22:42:16.0177053+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T22:42:16.0177236+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T22:42:16.0177717+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T22:42:16.0177818+00:00 xUnit.net INFORMATION         
2025-08-06T22:42:16.0177883+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T22:42:16.0178159+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T22:42:16.0185685+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.DataServiceCompletedSuccessfully [FAIL]
2025-08-06T22:42:16.0185894+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T22:42:16.0185945+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T22:42:16.0186088+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T22:42:16.0186156+00:00 xUnit.net INFORMATION         
2025-08-06T22:42:16.0186388+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T22:42:16.0186615+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T22:42:16.0189408+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.DatabaseServiceHealthy [FAIL]
2025-08-06T22:42:16.0189559+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T22:42:16.0189617+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T22:42:16.0189732+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T22:42:16.0189902+00:00 xUnit.net INFORMATION         
2025-08-06T22:42:16.0189954+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T22:42:16.0190156+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T22:42:16.0192381+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.AllServicesHealthy [FAIL]
2025-08-06T22:42:16.0193377+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T22:42:16.0193640+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T22:42:16.0193987+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T22:42:16.0194066+00:00 xUnit.net INFORMATION         
2025-08-06T22:42:16.0194119+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T22:42:16.0195491+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T22:42:16.0197850+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.DataServiceLogsIndicateSuccess [FAIL]
2025-08-06T22:42:16.0197902+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T22:42:16.0197925+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T22:42:16.0198210+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T22:42:16.0198262+00:00 xUnit.net INFORMATION         
2025-08-06T22:42:16.0198281+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T22:42:16.0198476+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T22:42:16.0199591+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.WebTests.GetWebResourceRootReturnsOkStatusCode [FAIL]
2025-08-06T22:42:16.0199702+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-06T22:42:16.0199724+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-06T22:42:16.0199779+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-06T22:42:16.0199807+00:00 xUnit.net INFORMATION         
2025-08-06T22:42:16.0199820+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-06T22:42:16.0200076+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-06T22:42:32.5602509+00:00 xUnit.net INFORMATION   Finished:    Miller.WMS.Edge.Tests
2025-08-06T22:42:32.5642375+00:00 xUnit.net INFORMATION === TEST EXECUTION SUMMARY ===
2025-08-06T22:42:32.5693238+00:00 xUnit.net INFORMATION    Miller.WMS.Edge.Tests  Total: 7, Errors: 0, Failed: 7, Skipped: 0, Not Run: 0, Time: 77.014s
