{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Miller.WMS.AppHost/1.0.0": {"dependencies": {"Aspire.Dashboard.Sdk.win-x64": "9.3.1", "Aspire.Hosting.AppHost": "9.3.1", "Aspire.Hosting.Orchestration.win-x64": "9.3.1", "Aspire.Hosting.Redis": "9.3.1"}, "runtime": {"Miller.WMS.AppHost.dll": {}}}, "Aspire.Dashboard.Sdk.win-x64/9.3.1": {}, "Aspire.Hosting/9.3.1": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Google.Protobuf": "3.30.2", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "16.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Hosting": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Http": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.5.2", "StreamJsonRpc": "2.21.69", "System.IO.Hashing": "9.0.4"}, "runtime": {"lib/net8.0/Aspire.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.300.125.30506"}}, "resources": {"lib/net8.0/cs/Aspire.Hosting.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Aspire.Hosting.resources.dll": {"locale": "de"}, "lib/net8.0/es/Aspire.Hosting.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Aspire.Hosting.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Aspire.Hosting.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Aspire.Hosting.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Aspire.Hosting.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Aspire.Hosting.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Aspire.Hosting.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Aspire.Hosting.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Aspire.Hosting.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Aspire.Hosting.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Aspire.Hosting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Aspire.Hosting.AppHost/9.3.1": {"dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.3.1", "Google.Protobuf": "3.30.2", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "16.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Hosting": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Http": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.5.2", "StreamJsonRpc": "2.21.69", "System.IO.Hashing": "9.0.4"}, "runtime": {"lib/net9.0/Aspire.Hosting.AppHost.dll": {"assemblyVersion": "*******", "fileVersion": "9.300.125.30506"}}}, "Aspire.Hosting.Orchestration.win-x64/9.3.1": {}, "Aspire.Hosting.Redis/9.3.1": {"dependencies": {"AspNetCore.HealthChecks.Redis": "9.0.0", "AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.3.1", "Google.Protobuf": "3.30.2", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "16.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Hosting": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Http": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.5.2", "StackExchange.Redis": "2.8.37", "StreamJsonRpc": "2.21.69", "System.IO.Hashing": "9.0.4"}, "runtime": {"lib/net8.0/Aspire.Hosting.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "9.300.125.30506"}}}, "AspNetCore.HealthChecks.Redis/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "StackExchange.Redis": "2.8.37"}, "runtime": {"lib/net8.0/HealthChecks.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCore.HealthChecks.Uris/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Http": "9.0.4"}, "runtime": {"lib/net8.0/HealthChecks.Uris.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Fractions/7.3.0": {"runtime": {"lib/netstandard2.1/Fractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Google.Protobuf/3.30.2": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Grpc.AspNetCore/2.71.0": {"dependencies": {"Google.Protobuf": "3.30.2", "Grpc.AspNetCore.Server.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0"}}, "Grpc.AspNetCore.Server/2.71.0": {"dependencies": {"Grpc.Net.Common": "2.71.0"}, "runtime": {"lib/net9.0/Grpc.AspNetCore.Server.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.AspNetCore.Server.ClientFactory/2.71.0": {"dependencies": {"Grpc.AspNetCore.Server": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0"}, "runtime": {"lib/net9.0/Grpc.AspNetCore.Server.ClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.Core.Api/2.71.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.Net.Client/2.71.0": {"dependencies": {"Grpc.Net.Common": "2.71.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}, "runtime": {"lib/net8.0/Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.Net.ClientFactory/2.71.0": {"dependencies": {"Grpc.Net.Client": "2.71.0", "Microsoft.Extensions.Http": "9.0.4"}, "runtime": {"lib/net8.0/Grpc.Net.ClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.Net.Common/2.71.0": {"dependencies": {"Grpc.Core.Api": "2.71.0"}, "runtime": {"lib/net8.0/Grpc.Net.Common.dll": {"assemblyVersion": "*******", "fileVersion": "2.71.0.0"}}}, "Grpc.Tools/2.72.0": {}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Json.More.Net/2.1.0": {"runtime": {"lib/net9.0/Json.More.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.0.0"}}}, "JsonPatch.Net/3.3.0": {"dependencies": {"JsonPointer.Net": "5.2.0"}, "runtime": {"lib/net9.0/JsonPatch.Net.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.3.0.0"}}}, "JsonPointer.Net/5.2.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Json.More.Net": "2.1.0"}, "runtime": {"lib/net9.0/JsonPointer.Net.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.2.0.0"}}}, "KubernetesClient/16.0.7": {"dependencies": {"Fractions": "7.3.0", "YamlDotNet": "16.3.0"}, "runtime": {"lib/net9.0/KubernetesClient.dll": {"assemblyVersion": "********", "fileVersion": "16.0.7.57311"}}}, "MessagePack/2.5.192": {"dependencies": {"MessagePack.Annotations": "2.5.192", "Microsoft.NET.StringTools": "17.6.3"}, "runtime": {"lib/net6.0/MessagePack.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.192.54228"}}}, "MessagePack.Annotations/2.5.192": {"runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.192.54228"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Json": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Diagnostics/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.4": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16403"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16403"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileSystemGlobbing": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Hosting/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.Configuration.CommandLine": "9.0.4", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.4", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.4", "Microsoft.Extensions.Configuration.Json": "9.0.4", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.4", "Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Configuration": "9.0.4", "Microsoft.Extensions.Logging.Console": "9.0.4", "Microsoft.Extensions.Logging.Debug": "9.0.4", "Microsoft.Extensions.Logging.EventLog": "9.0.4", "Microsoft.Extensions.Logging.EventSource": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Http/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.Console/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Configuration": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.Debug/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "System.Diagnostics.EventLog": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Options/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Primitives/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.NET.StringTools/17.6.3": {"runtime": {"lib/net7.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "*******", "fileVersion": "17.6.3.22601"}}}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"dependencies": {"Microsoft.VisualStudio.Validation": "17.8.8"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Threading.dll": {"assemblyVersion": "17.13.0.0", "fileVersion": "17.13.61.36374"}}, "resources": {"lib/net8.0/cs/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Validation/17.8.8": {"runtime": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"assemblyVersion": "17.8.0.0", "fileVersion": "17.8.8.15457"}}, "resources": {"lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Nerdbank.Streams/2.11.90": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8", "System.IO.Pipelines": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.1.0"}, "runtime": {"lib/net6.0/Nerdbank.Streams.dll": {"assemblyVersion": "********", "fileVersion": "2.11.90.55113"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"dependencies": {"System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}}, "Polly.Core/8.5.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.5.2.4319"}}}, "StackExchange.Redis/2.8.37": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net8.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.8.37.59676"}}}, "StreamJsonRpc/2.21.69": {"dependencies": {"MessagePack": "2.5.192", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8", "Nerdbank.Streams": "2.11.90", "Newtonsoft.Json": "13.0.3", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/StreamJsonRpc.dll": {"assemblyVersion": "********", "fileVersion": "2.21.69.19811"}}, "resources": {"lib/net8.0/cs/StreamJsonRpc.resources.dll": {"locale": "cs"}, "lib/net8.0/de/StreamJsonRpc.resources.dll": {"locale": "de"}, "lib/net8.0/es/StreamJsonRpc.resources.dll": {"locale": "es"}, "lib/net8.0/fr/StreamJsonRpc.resources.dll": {"locale": "fr"}, "lib/net8.0/it/StreamJsonRpc.resources.dll": {"locale": "it"}, "lib/net8.0/ja/StreamJsonRpc.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/StreamJsonRpc.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/StreamJsonRpc.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/StreamJsonRpc.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/StreamJsonRpc.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/StreamJsonRpc.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/StreamJsonRpc.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/StreamJsonRpc.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Diagnostics.EventLog/9.0.4": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.IO.Hashing/9.0.4": {"runtime": {"lib/net9.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.IO.Pipelines/8.0.0": {}, "System.Runtime.CompilerServices.Unsafe/6.1.0": {}, "YamlDotNet/16.3.0": {"runtime": {"lib/net8.0/YamlDotNet.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}}}, "libraries": {"Miller.WMS.AppHost/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Aspire.Dashboard.Sdk.win-x64/9.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-0BAjVEMMBOkGaxVK3AioTiJKN2nyB+0hjvf8k2ek9LUIMowE2WvQfGUZhdZJaqmfqUCMdmFuhqNzkgubclIcXQ==", "path": "aspire.dashboard.sdk.win-x64/9.3.1", "hashPath": "aspire.dashboard.sdk.win-x64.9.3.1.nupkg.sha512"}, "Aspire.Hosting/9.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-9yVRi8TFV2VQD/7UFT2RezaPKa/91JisKIADAyrkaqJzNFBRQbh/z7xi4MQ6Bzugav/atOdAx4K/NCBdC6LZTg==", "path": "aspire.hosting/9.3.1", "hashPath": "aspire.hosting.9.3.1.nupkg.sha512"}, "Aspire.Hosting.AppHost/9.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-U7lENUUkk97FpGsu6BLi2OA9RNaHKriyLw/t6Q/YOZrsch3PoEH09Hs7UCHfZIErKT40BczHjOvGP0uebNU8Tw==", "path": "aspire.hosting.apphost/9.3.1", "hashPath": "aspire.hosting.apphost.9.3.1.nupkg.sha512"}, "Aspire.Hosting.Orchestration.win-x64/9.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-8tA+G0AHRF2DCg0WVwvXTwDGZYXetRlG9E+5UIfdB10lj6MqOSEVr6/1h9lIAQuTwy8HWtI7o8GyDotKg5AWeQ==", "path": "aspire.hosting.orchestration.win-x64/9.3.1", "hashPath": "aspire.hosting.orchestration.win-x64.9.3.1.nupkg.sha512"}, "Aspire.Hosting.Redis/9.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-RJzbON2N0cj/WlYyMRhyExKBUHDwcsbYGfI0eMZPjgoVEkQz0MuYjDeAVxRw3DxCn5QK7BVYqPQ0D09wDidGAw==", "path": "aspire.hosting.redis/9.3.1", "hashPath": "aspire.hosting.redis.9.3.1.nupkg.sha512"}, "AspNetCore.HealthChecks.Redis/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yNH0h8GLRbAf+PU5HNVLZ5hNeyq9mDVmRKO9xuZsme/znUYoBJlQvI0gq45gaZNlLncCHkMhR4o90MuT+gxxPw==", "path": "aspnetcore.healthchecks.redis/9.0.0", "hashPath": "aspnetcore.healthchecks.redis.9.0.0.nupkg.sha512"}, "AspNetCore.HealthChecks.Uris/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYdNlA437KeF8p9qOpZFyNqAN+c0FXt/JjTvzH/Qans0q0O3pPE8KPnn39ucQQjR/Roum1vLTP3kXiUs8VHyuA==", "path": "aspnetcore.healthchecks.uris/9.0.0", "hashPath": "aspnetcore.healthchecks.uris.9.0.0.nupkg.sha512"}, "Fractions/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2bETFWLBc8b7Ut2SVi+bxhGVwiSpknHYGBh2PADyGWONLkTxT7bKyDRhF8ao+XUv90tq8Fl7GTPxSI5bacIRJw==", "path": "fractions/7.3.0", "hashPath": "fractions.7.3.0.nupkg.sha512"}, "Google.Protobuf/3.30.2": {"type": "package", "serviceable": true, "sha512": "sha512-Y2aOVLIt75yeeEWigg9V9YnjsEm53sADtLGq0gLhwaXpk3iu8tYSoauolyhenagA2sWno2TQ2WujI0HQd6s1Vw==", "path": "google.protobuf/3.30.2", "hashPath": "google.protobuf.3.30.2.nupkg.sha512"}, "Grpc.AspNetCore/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-B4wAbNtAuHNiHAMxLFWL74wUElzNOOboFnypalqpX76piCOGz/w5FpilbVVYGboI4Qgl4ZmZsvDZ1zLwHNsjnw==", "path": "grpc.aspnetcore/2.71.0", "hashPath": "grpc.aspnetcore.2.71.0.nupkg.sha512"}, "Grpc.AspNetCore.Server/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-kv+9YVB6MqDYWIcstXvWrT7Xc1si/sfINzzSxvQfjC3aei+92gXDUXCH/Q+TEvi4QSICRqu92BYcrXUBW7cuOw==", "path": "grpc.aspnetcore.server/2.71.0", "hashPath": "grpc.aspnetcore.server.2.71.0.nupkg.sha512"}, "Grpc.AspNetCore.Server.ClientFactory/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-AHvMxoC+esO1e/nOYBjxvn0WDHAfglcVBjtkBy6ohgnV+PzkF8UdkPHE02xnyPFaSokWGZKnWzjgd00x6EZpyQ==", "path": "grpc.aspnetcore.server.clientfactory/2.71.0", "hashPath": "grpc.aspnetcore.server.clientfactory.2.71.0.nupkg.sha512"}, "Grpc.Core.Api/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-QquqUC37yxsDzd1QaDRsH2+uuznWPTS8CVE2Yzwl3CvU4geTNkolQXoVN812M2IwT6zpv3jsZRc9ExJFNFslTg==", "path": "grpc.core.api/2.71.0", "hashPath": "grpc.core.api.2.71.0.nupkg.sha512"}, "Grpc.Net.Client/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-U1vr20r5ngoT9nlb7wejF28EKN+taMhJsV9XtK9MkiepTZwnKxxiarriiMfCHuDAfPUm9XUjFMn/RIuJ4YY61w==", "path": "grpc.net.client/2.71.0", "hashPath": "grpc.net.client.2.71.0.nupkg.sha512"}, "Grpc.Net.ClientFactory/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-8oPLwQLPo86fmcf9ghjCDyNsSWhtHc3CXa/AqwF8Su/pG7qAoeWWtbymsZhoNvCV9Zjzb6BDcIPKXLYt+O175g==", "path": "grpc.net.clientfactory/2.71.0", "hashPath": "grpc.net.clientfactory.2.71.0.nupkg.sha512"}, "Grpc.Net.Common/2.71.0": {"type": "package", "serviceable": true, "sha512": "sha512-v0c8R97TwRYwNXlC8GyRXwYTCNufpDfUtj9la+wUrZFzVWkFJuNAltU+c0yI3zu0jl54k7en6u2WKgZgd57r2Q==", "path": "grpc.net.common/2.71.0", "hashPath": "grpc.net.common.2.71.0.nupkg.sha512"}, "Grpc.Tools/2.72.0": {"type": "package", "serviceable": true, "sha512": "sha512-BCiuQ03EYjLHCo9hqZmY5barsz5vvcz/+/ICt5wCbukaePHZmMPDGelKlkxWx3q+f5xOMNHa9zXQ2N6rQZ4B+w==", "path": "grpc.tools/2.72.0", "hashPath": "grpc.tools.2.72.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Json.More.Net/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-qtwsyAsL55y2vB2/sK4Pjg3ZyVzD5KKSpV3lOAMHlnjFfsjQ/86eHJfQT9aV1YysVXzF4+xyHOZbh7Iu3YQ7Lg==", "path": "json.more.net/2.1.0", "hashPath": "json.more.net.2.1.0.nupkg.sha512"}, "JsonPatch.Net/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GIcMMDtzfzVfIpQgey8w7dhzcw6jG5nD4DDAdQCTmHfblkCvN7mI8K03to8YyUhKMl4PTR6D6nLSvWmyOGFNTg==", "path": "jsonpatch.net/3.3.0", "hashPath": "jsonpatch.net.3.3.0.nupkg.sha512"}, "JsonPointer.Net/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qe1F7Tr/p4mgwLPU9P60MbYkp+xnL2uCPnWXGgzfR/AZCunAZIC0RZ32dLGJJEhSuLEfm0YF/1R3u5C7mEVq+w==", "path": "jsonpointer.net/5.2.0", "hashPath": "jsonpointer.net.5.2.0.nupkg.sha512"}, "KubernetesClient/16.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-hH+YN18bpIRO/rq2CiMGDpLpc/KjSMlAn4EelFB4PgiswbSie4jANLAOou1Q39Kx7en2jO1Qp73y3SkjxGJIMg==", "path": "kubernetesclient/16.0.7", "hashPath": "kubernetesclient.16.0.7.nupkg.sha512"}, "MessagePack/2.5.192": {"type": "package", "serviceable": true, "sha512": "sha512-Jtle5MaFeIFkdXtxQeL9Tu2Y3HsAQGoSntOzrn6Br/jrl6c8QmG22GEioT5HBtZJR0zw0s46OnKU8ei2M3QifA==", "path": "messagepack/2.5.192", "hashPath": "messagepack.2.5.192.nupkg.sha512"}, "MessagePack.Annotations/2.5.192": {"type": "package", "serviceable": true, "sha512": "sha512-ja<PERSON><PERSON>w<PERSON>govWIZ8Zysdyf3b7b34/BrADw4v82GaEZymUhDd3ScMPrYd/cttekeDteJJPXseJxp04yTIcxiVUjTWg==", "path": "messagepack.annotations/2.5.192", "hashPath": "messagepack.annotations.2.5.192.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-KIVBrMbItnCJDd1RF4KEaE8jZwDJcDUJW5zXpbwQ05HNYTK1GveHxHK0B3SjgDJuR48GRACXAO+BLhL8h34S7g==", "path": "microsoft.extensions.configuration/9.0.4", "hashPath": "microsoft.extensions.configuration.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0LN/DiIKvBrkqp7gkF3qhGIeZk6/B63PthAHjQsxymJfIBcz0kbf4/p/t4lMgggVxZ+flRi5xvTwlpPOoZk8fg==", "path": "microsoft.extensions.configuration.abstractions/9.0.4", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cdrjcl9RIcwt3ECbnpP0Gt1+pkjdW90mq5yFYy8D9qRj2NqFFcv3yDp141iEamsd9E218sGxK8WHaIOcrqgDJg==", "path": "microsoft.extensions.configuration.binder/9.0.4", "hashPath": "microsoft.extensions.configuration.binder.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-TbM2HElARG7z1gxwakdppmOkm1SykPqDcu3EF97daEwSb/+TXnRrFfJtF+5FWWxcsNhbRrmLfS2WszYcab7u1A==", "path": "microsoft.extensions.configuration.commandline/9.0.4", "hashPath": "microsoft.extensions.configuration.commandline.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-2IGiG3FtVnD83IA6HYGuNei8dOw455C09yEhGl8bjcY6aGZgoC6yhYvDnozw8wlTowfoG9bxVrdTsr2ACZOYHg==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.4", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UY864WQ3AS2Fkc8fYLombWnjrXwYt+BEHHps0hY4sxlgqaVW06AxbpgRZjfYf8PyRbplJqruzZDB/nSLT+7RLQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.4", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-vVXI70CgT/dmXV3MM+n/BR2rLXEoAyoK0hQT+8MrbCMuJBiLRxnTtSrksNiASWCwOtxo/Tyy7CO8AGthbsYxnw==", "path": "microsoft.extensions.configuration.json/9.0.4", "hashPath": "microsoft.extensions.configuration.json.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-zuvyC72gJkJyodyGowCuz3EQ1QvzNXJtKusuRzmjoHr17aeB3X0aSiKFB++HMHnQIWWlPOBf9YHTQfEqzbgl1g==", "path": "microsoft.extensions.configuration.usersecrets/9.0.4", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-f2MTUaS2EQ3lX4325ytPAISZqgBfXmY0WvgD80ji6Z20AoDNiCESxsqo6mFRwHJD/jfVKRw9FsW6+86gNre3ug==", "path": "microsoft.extensions.dependencyinjection/9.0.4", "hashPath": "microsoft.extensions.dependencyinjection.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UI0TQPVkS78bFdjkTodmkH0Fe8lXv9LnhGFKgKrsgUJ5a5FVdFRcgjIkBVLbGgdRhxWirxH/8IXUtEyYJx6GQg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.4", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-1bCSQrGv9+bpF5MGKF6THbnRFUZqQDrWPA39NDeVW9djeHBmow8kX4SX6/8KkeKI8gmUDG7jsG/bVuNAcY/ATQ==", "path": "microsoft.extensions.diagnostics/9.0.4", "hashPath": "microsoft.extensions.diagnostics.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-IAucBcHYtiCmMyFag+Vrp5m+cjGRlDttJk9Vx7Dqpq+Ama4BzVUOk0JARQakgFFr7ZTBSgLKlHmtY5MiItB7Cg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.4", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-jW9lhWQzOOL5sBUCNtAiS6B7tGeLlxJVDjwNuQAQl6dDt9PAAxt3+T2F2jtcvi7KoujgzAdkKQKtGoRaAGlD9w==", "path": "microsoft.extensions.diagnostics.healthchecks/9.0.4", "hashPath": "microsoft.extensions.diagnostics.healthchecks.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-XM6WwNbDkVuGhDN89eKxA2Og2eMDXB0PVI7PEzl2R0MbFjYUlfTh7D7vBPEWUVCf2zPDAFiwcMlnVzi6Umq5mg==", "path": "microsoft.extensions.diagnostics.healthchecks.abstractions/9.0.4", "hashPath": "microsoft.extensions.diagnostics.healthchecks.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-gQN2o/KnBfVk6Bd71E2YsvO5lsqrqHmaepDGk+FB/C4aiQY9B0XKKNKfl5/TqcNOs9OEithm4opiMHAErMFyEw==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.4", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-qkQ9V7KFZdTWNThT7ke7E/Jad38s46atSs3QUYZB8f3thBTrcrousdY4Y/tyCtcH5YjsPSiByjuN+L8W/ThMQg==", "path": "microsoft.extensions.fileproviders.physical/9.0.4", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-05Lh2ItSk4mzTdDWATW9nEcSybwprN8Tz42Fs5B+jwdXUpauktdAQUI1Am4sUQi2C63E5hvQp8gXvfwfg9mQGQ==", "path": "microsoft.extensions.filesystemglobbing/9.0.4", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-1rZwLE+tTUIyZRUzmlk/DQj+v+Eqox+rjb+X7Fi+cYTbQfIZPYwpf1pVybsV3oje8+Pe4GaNukpBVUlPYeQdeQ==", "path": "microsoft.extensions.hosting/9.0.4", "hashPath": "microsoft.extensions.hosting.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-bXkwRPMo4x19YKH6/V9XotU7KYQJlihXhcWO1RDclAY3yfY3XNg4QtSEBvng4kK/DnboE0O/nwSl+6Jiv9P+FA==", "path": "microsoft.extensions.hosting.abstractions/9.0.4", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-ezelU6HJgmq4862YoWuEbHGSV+JnfnonTSbNSJVh6n6wDehyiJn4hBtcK7rGbf2KO3QeSvK5y8E7uzn1oaRH5w==", "path": "microsoft.extensions.http/9.0.4", "hashPath": "microsoft.extensions.http.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-xW6QPYsqhbuWBO9/1oA43g/XPKbohJx+7G8FLQgQXIriYvY7s+gxr2wjQJfRoPO900dvvv2vVH7wZovG+M1m6w==", "path": "microsoft.extensions.logging/9.0.4", "hashPath": "microsoft.extensions.logging.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0MXlimU4Dud6t+iNi5NEz3dO2w1HXdhoOLaYFuLPCjAsvlPQGwOT6V2KZRMLEhCAm/stSZt1AUv0XmDdkjvtbw==", "path": "microsoft.extensions.logging.abstractions/9.0.4", "hashPath": "microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-/kF+rSnoo3/nIwGzWsR4RgBnoTOdZ3lzz2qFRyp/GgaNid4j6hOAQrs/O+QHXhlcAdZxjg37MvtIE+pAvIgi9g==", "path": "microsoft.extensions.logging.configuration/9.0.4", "hashPath": "microsoft.extensions.logging.configuration.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cI0lQe0js65INCTCtAgnlVJWKgzgoRHVAW1B1zwCbmcliO4IZoTf92f1SYbLeLk7FzMJ/GlCvjLvJegJ6kltmQ==", "path": "microsoft.extensions.logging.console/9.0.4", "hashPath": "microsoft.extensions.logging.console.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-D1jy+jy+huUUxnkZ0H480RZK8vqKn8NsQxYpMpPL/ALPPh1WATVLcr/uXI3RUBB45wMW5265O+hk9x3jnnXFuA==", "path": "microsoft.extensions.logging.debug/9.0.4", "hashPath": "microsoft.extensions.logging.debug.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-bApxdklf7QTsONOLR5ow6SdDFXR5ncHvumSEg2+QnCvxvkzc2z5kNn7yQCyupRLRN4jKbnlTkVX8x9qLlwL6Qg==", "path": "microsoft.extensions.logging.eventlog/9.0.4", "hashPath": "microsoft.extensions.logging.eventlog.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-R600zTxVJNw2IeAEOvdOJGNA1lHr1m3vo460hSF5G1DjwP0FNpyeH4lpLDMuf34diKwB1LTt5hBw1iF1/iuwsQ==", "path": "microsoft.extensions.logging.eventsource/9.0.4", "hashPath": "microsoft.extensions.logging.eventsource.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-fiFI2+58kicqVZyt/6obqoFwHiab7LC4FkQ3mmiBJ28Yy4fAvy2+v9MRnSvvlOO8chTOjKsdafFl/K9veCPo5g==", "path": "microsoft.extensions.options/9.0.4", "hashPath": "microsoft.extensions.options.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-aridVhAT3Ep+vsirR1pzjaOw0Jwiob6dc73VFQn2XmDfBA2X98M8YKO1GarvsXRX7gX1Aj+hj2ijMzrMHDOm0A==", "path": "microsoft.extensions.options.configurationextensions/9.0.4", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "path": "microsoft.extensions.primitives/9.0.4", "hashPath": "microsoft.extensions.primitives.9.0.4.nupkg.sha512"}, "Microsoft.NET.StringTools/17.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-N0ZIanl1QCgvUumEL1laasU0a7sOE5ZwLZVTn0pAePnfhq8P7SvTjF8Axq+CnavuQkmdQpGNXQ1efZtu5kDFbA==", "path": "microsoft.net.stringtools/17.6.3", "hashPath": "microsoft.net.stringtools.17.6.3.nupkg.sha512"}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"type": "package", "serviceable": true, "sha512": "sha512-vl5a2URJYCO5m+aZZtNlAXAMz28e2pUotRuoHD7RnCWOCeoyd8hWp5ZBaLNYq4iEj2oeJx5ZxiSboAjVmB20Qg==", "path": "microsoft.visualstudio.threading.only/17.13.61", "hashPath": "microsoft.visualstudio.threading.only.17.13.61.nupkg.sha512"}, "Microsoft.VisualStudio.Validation/17.8.8": {"type": "package", "serviceable": true, "sha512": "sha512-rWXThIpyQd4YIXghNkiv2+VLvzS+MCMKVRDR0GAMlflsdo+YcAN2g2r5U1Ah98OFjQMRexTFtXQQ2LkajxZi3g==", "path": "microsoft.visualstudio.validation/17.8.8", "hashPath": "microsoft.visualstudio.validation.17.8.8.nupkg.sha512"}, "Nerdbank.Streams/2.11.90": {"type": "package", "serviceable": true, "sha512": "sha512-7jrOfU6b/PVBccqzNLfw9u84WWzkSpvWLb2mZxvwdQkOx/V9FXWkmnp/rjOnBFDOhrO/ev4+gQ5QS13FkgNSBA==", "path": "nerdbank.streams/2.11.90", "hashPath": "nerdbank.streams.2.11.90.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "Polly.Core/8.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-1MJKdxv4zwDmiWvYvVN24DsrWUfgQ4F83voH8bhbtLMdPuGy8CfTUzsgQhvyrl1a7hrM6f/ydwLVdVUI0xooUw==", "path": "polly.core/8.5.2", "hashPath": "polly.core.8.5.2.nupkg.sha512"}, "StackExchange.Redis/2.8.37": {"type": "package", "serviceable": true, "sha512": "sha512-ULscDAQR+z132IsLczXcM/PG+EyfQMNxhvqjG1Xd/h/J1O9prsUU4pG9d95xv77lUF8v3X6owpf/11Uh0uvZAQ==", "path": "stackexchange.redis/2.8.37", "hashPath": "stackexchange.redis.2.8.37.nupkg.sha512"}, "StreamJsonRpc/2.21.69": {"type": "package", "serviceable": true, "sha512": "sha512-WbTpn/PIo+HpFYnsOCiOOe0kHUE2N1eiVRi7MO70DFBTMG3pAOfrgHtwUpOJ37dfDETq/9P9WNIbHom4ABZfrA==", "path": "streamjsonrpc/2.21.69", "hashPath": "streamjsonrpc.2.21.69.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-getRQEXD8idlpb1KW56XuxImMy0FKp2WJPDf3Qr0kI/QKxxJSftqfDFVo0DZ3HCJRLU73qHSruv5q2l5O47jQQ==", "path": "system.diagnostics.eventlog/9.0.4", "hashPath": "system.diagnostics.eventlog.9.0.4.nupkg.sha512"}, "System.IO.Hashing/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-WogPvgAFqQORFD8Iyha6RZ+/1QB3dsWRWxbwi8/HHVgiGQ8z0oMWpwe8Kk3Ti+Roe+P6a3sBg+WwBfEsyziZKg==", "path": "system.io.hashing/9.0.4", "hashPath": "system.io.hashing.9.0.4.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-5o/HZxx6RVqYlhKSq8/zronDkALJZUT2Vz0hx43f0gwe8mwlM0y2nYlqdBwLMzr262Bwvpikeb/yEwkAa5PADg==", "path": "system.runtime.compilerservices.unsafe/6.1.0", "hashPath": "system.runtime.compilerservices.unsafe.6.1.0.nupkg.sha512"}, "YamlDotNet/16.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SgMOdxbz8X65z8hraIs6hOEdnkH6hESTAIUa7viEngHOYaH+6q5XJmwr1+yb9vJpNQ19hCQY69xbFsLtXpobQA==", "path": "yamldotnet/16.3.0", "hashPath": "yamldotnet.16.3.0.nupkg.sha512"}}}