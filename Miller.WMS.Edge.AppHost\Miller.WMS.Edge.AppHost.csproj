<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.3.1" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UserSecretsId>0fb3be66-4f00-43d0-8571-b1e1d864aed3</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Miller.WMS.Core.DataService\Miller.WMS.Core.DataService.csproj" />
    <ProjectReference Include="..\Miller.WMS.Edge.ApiService\Miller.WMS.Edge.ApiService.csproj" />
    <ProjectReference Include="..\Miller.WMS.Edge.Web\Miller.WMS.Edge.Web.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.AppConfiguration" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.AppContainers" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.ApplicationInsights" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.CosmosDB" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.EventHubs" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.KeyVault" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.PostgreSQL" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.Redis" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.Search" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.ServiceBus" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.Sql" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Azure.Storage" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Keycloak" Version="9.3.1-preview.1.25305.6" />
    <PackageReference Include="Aspire.Hosting.MongoDB" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.MySql" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.PostgreSQL" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.RabbitMQ" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Redis" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.SqlServer" Version="9.4.0" />
    <PackageReference Include="CommunityToolkit.Aspire.Hosting.Java" Version="9.7.0" />
    <PackageReference Include="CommunityToolkit.Aspire.Hosting.MongoDB.Extensions" Version="9.7.0" />
    <PackageReference Include="CommunityToolkit.Aspire.Hosting.PostgreSQL.Extensions" Version="9.7.0" />
    <PackageReference Include="CommunityToolkit.Aspire.Hosting.Redis.Extensions" Version="9.7.0" />
    <PackageReference Include="CommunityToolkit.Aspire.Hosting.SqlServer.Extensions" Version="9.7.0" />
    <PackageReference Include="Elastic.Aspire.Hosting.Elasticsearch" Version="9.3.0" />

  </ItemGroup>

</Project>
