2025-08-08T21:58:51.0791918+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Version: 1.7.3+5abdae1f2e07071c4e81b27ac262f241708ec3cf
2025-08-08T21:58:51.0880287+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging mode: asynchronous
2025-08-08T21:58:51.0901530+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Logging level: Information
2025-08-08T21:58:51.0901641+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION CreateBuilderAsync entry time: 21:58:51.053
2025-08-08T21:58:51.0936529+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION PID: 17700
2025-08-08T21:58:51.0936895+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime information: win-x64 - .NET 9.0.7
2025-08-08T21:58:51.0937000+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Runtime location: C:\Program Files\dotnet\shared\Microsoft.NETCore.App\9.0.7\System.Private.CoreLib.dll
2025-08-08T21:58:51.0937091+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION IsDynamicCodeSupported: True
2025-08-08T21:58:51.0937877+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Test module: C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Miller.WMS.Edge.Tests.dll
2025-08-08T21:58:51.0938692+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION Command line arguments: '--server --diagnostic --diagnostic-verbosity Information --diagnostic-output-directory C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\bin\Debug\net9.0\Log --results-directory C:\_\Miller_Github\Miller.WMS\TestResults --client-port 58126'
2025-08-08T21:58:51.0950353+00:00 Microsoft.Testing.Platform.Builder.TestApplication INFORMATION TESTINGPLATFORM_DEFAULT_HANG_TIMEOUT: ''
2025-08-08T21:58:51.1041239+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting RegisterEnvironmentVariablesConfigurationSource: 'True'
2025-08-08T21:58:51.1167424+00:00 Microsoft.Testing.Platform.Hosts.TestHostBuilder INFORMATION Setting PlatformExitProcessOnUnhandledException: 'False', config file: False environment variable: 
2025-08-08T21:58:51.3107003+00:00 Microsoft.Testing.Platform.Requests.TestHostTestFrameworkInvoker INFORMATION Test framework UID: '30ea7c6e-dd24-4152-a360-1387158cd41d' Version: '3.0.0' DisplayName: 'xUnit.net v3 Microsoft.Testing.Platform test framework' Description: 'xUnit.net v3 Microsoft.Testing.Platform test framework'
2025-08-08T21:58:51.3142491+00:00 xUnit.net INFORMATION xUnit.net v3 In-Process Runner v3.0.0+d0213fc4e5 (64-bit .NET 9.0.7)
2025-08-08T21:58:51.3488623+00:00 xUnit.net INFORMATION   Discovering: Miller.WMS.Edge.Tests
2025-08-08T21:58:51.3804604+00:00 xUnit.net INFORMATION   Discovered:  Miller.WMS.Edge.Tests
2025-08-08T21:58:51.3975884+00:00 xUnit.net INFORMATION   Starting:    Miller.WMS.Edge.Tests
2025-08-08T21:59:51.6585294+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.ApiTests.GetApiResourceRootReturnsOkStatusCode [FAIL]
2025-08-08T21:59:51.6594439+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-08T21:59:51.6594958+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-08T21:59:51.6599775+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-08T21:59:51.6603675+00:00 xUnit.net INFORMATION         
2025-08-08T21:59:51.6603933+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-08T21:59:51.6604289+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-08T21:59:51.6651557+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.ApiTests.GetPingEndpointReturnsTrue [FAIL]
2025-08-08T21:59:51.6651810+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-08T21:59:51.6651957+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-08T21:59:51.6652138+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-08T21:59:51.6652201+00:00 xUnit.net INFORMATION         
2025-08-08T21:59:51.6652240+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-08T21:59:51.6652412+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-08T21:59:51.6656604+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.CdcWorkerTests.CdcWorker_Ping_ReturnsTrue [FAIL]
2025-08-08T21:59:51.6656744+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-08T21:59:51.6656795+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-08T21:59:51.6656887+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-08T21:59:51.6656959+00:00 xUnit.net INFORMATION         
2025-08-08T21:59:51.6657069+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-08T21:59:51.6657231+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-08T21:59:51.6659760+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.DataServiceCompletedSuccessfully [FAIL]
2025-08-08T21:59:51.6659884+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-08T21:59:51.6659913+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-08T21:59:51.6660067+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-08T21:59:51.6660130+00:00 xUnit.net INFORMATION         
2025-08-08T21:59:51.6660162+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-08T21:59:51.6660289+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-08T21:59:51.6661660+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.DatabaseServiceHealthy [FAIL]
2025-08-08T21:59:51.6661815+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-08T21:59:51.6661914+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-08T21:59:51.6662050+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-08T21:59:51.6662096+00:00 xUnit.net INFORMATION         
2025-08-08T21:59:51.6662126+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-08T21:59:51.6662262+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-08T21:59:51.6663657+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.AllServicesHealthy [FAIL]
2025-08-08T21:59:51.6663717+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-08T21:59:51.6663739+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-08T21:59:51.6663851+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-08T21:59:51.6663885+00:00 xUnit.net INFORMATION         
2025-08-08T21:59:51.6663920+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-08T21:59:51.6664085+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-08T21:59:51.6665132+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.DataTests.DataServiceLogsIndicateSuccess [FAIL]
2025-08-08T21:59:51.6665238+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-08T21:59:51.6665261+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-08T21:59:51.6665317+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-08T21:59:51.6665348+00:00 xUnit.net INFORMATION         
2025-08-08T21:59:51.6665372+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-08T21:59:51.6665521+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-08T21:59:51.6667223+00:00 xUnit.net ERROR     Miller.WMS.Edge.Tests.WebTests.GetWebResourceRootReturnsOkStatusCode [FAIL]
2025-08-08T21:59:51.6667320+00:00 xUnit.net INFORMATION       Collection fixture type 'Miller.WMS.Edge.Tests.AspireTestFixture' threw in InitializeAsync
2025-08-08T21:59:51.6667345+00:00 xUnit.net INFORMATION       ---- System.TimeoutException : The operation has timed out.
2025-08-08T21:59:51.6667477+00:00 xUnit.net INFORMATION       Stack Trace:
2025-08-08T21:59:51.6667517+00:00 xUnit.net INFORMATION         
2025-08-08T21:59:51.6667545+00:00 xUnit.net INFORMATION         ----- Inner Stack Trace -----
2025-08-08T21:59:51.6667660+00:00 xUnit.net INFORMATION         C:\_\Miller_Github\Miller.WMS\Miller.WMS.Edge.Tests\AspireTestFixture.cs(44,0): at Miller.WMS.Edge.Tests.AspireTestFixture.InitializeAsync()
2025-08-08T21:59:51.7086553+00:00 Microsoft.Testing.Platform.Hosts.ServerTestHost WARNING [ServerTestHost.OnTaskSchedulerUnobservedTaskException] Unhandled exception: System.AggregateException: A Task's exception(s) were not observed either by Waiting on the Task or accessing its Exception property. As a result, the unobserved exception was rethrown by the finalizer thread. (The request was aborted.)
 ---> System.IO.IOException: The request was aborted.
   at System.Net.Http.Http2Connection.ThrowRequestAborted(Exception innerException)
   at System.Net.Http.Http2Connection.Http2Stream.TryReadFromBuffer(Span`1 buffer, Boolean partOfSyncRead)
   at System.Net.Http.Http2Connection.Http2Stream.ReadDataAsync(Memory`1 buffer, HttpResponseMessage responseMessage, CancellationToken cancellationToken)
   at k8s.LineSeparatedHttpContent.CancelableStream.ReadAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at System.IO.StreamReader.ReadBufferAsync(CancellationToken cancellationToken)
   at System.IO.StreamReader.ReadLineAsyncInternal(CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
2025-08-08T21:59:51.7101617+00:00 Microsoft.Testing.Platform.Hosts.ServerTestHost WARNING [ServerTestHost.OnTaskSchedulerUnobservedTaskException] Unhandled exception: System.AggregateException: A Task's exception(s) were not observed either by Waiting on the Task or accessing its Exception property. As a result, the unobserved exception was rethrown by the finalizer thread. (The request was aborted.)
 ---> System.IO.IOException: The request was aborted.
   at System.Net.Http.Http2Connection.ThrowRequestAborted(Exception innerException)
   at System.Net.Http.Http2Connection.Http2Stream.TryReadFromBuffer(Span`1 buffer, Boolean partOfSyncRead)
   at System.Net.Http.Http2Connection.Http2Stream.ReadDataAsync(Memory`1 buffer, HttpResponseMessage responseMessage, CancellationToken cancellationToken)
   at k8s.LineSeparatedHttpContent.CancelableStream.ReadAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at System.IO.StreamReader.ReadBufferAsync(CancellationToken cancellationToken)
   at System.IO.StreamReader.ReadLineAsyncInternal(CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
2025-08-08T21:59:51.7110846+00:00 Microsoft.Testing.Platform.Hosts.ServerTestHost WARNING [ServerTestHost.OnTaskSchedulerUnobservedTaskException] Unhandled exception: System.AggregateException: A Task's exception(s) were not observed either by Waiting on the Task or accessing its Exception property. As a result, the unobserved exception was rethrown by the finalizer thread. (The request was aborted.)
 ---> System.IO.IOException: The request was aborted.
   at System.Net.Http.Http2Connection.ThrowRequestAborted(Exception innerException)
   at System.Net.Http.Http2Connection.Http2Stream.TryReadFromBuffer(Span`1 buffer, Boolean partOfSyncRead)
   at System.Net.Http.Http2Connection.Http2Stream.ReadDataAsync(Memory`1 buffer, HttpResponseMessage responseMessage, CancellationToken cancellationToken)
   at k8s.LineSeparatedHttpContent.CancelableStream.ReadAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at System.IO.StreamReader.ReadBufferAsync(CancellationToken cancellationToken)
   at System.IO.StreamReader.ReadLineAsyncInternal(CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
2025-08-08T21:59:51.7121430+00:00 Microsoft.Testing.Platform.Hosts.ServerTestHost WARNING [ServerTestHost.OnTaskSchedulerUnobservedTaskException] Unhandled exception: System.AggregateException: A Task's exception(s) were not observed either by Waiting on the Task or accessing its Exception property. As a result, the unobserved exception was rethrown by the finalizer thread. (The request was aborted.)
 ---> System.IO.IOException: The request was aborted.
   at System.Net.Http.Http2Connection.ThrowRequestAborted(Exception innerException)
   at System.Net.Http.Http2Connection.Http2Stream.TryReadFromBuffer(Span`1 buffer, Boolean partOfSyncRead)
   at System.Net.Http.Http2Connection.Http2Stream.ReadDataAsync(Memory`1 buffer, HttpResponseMessage responseMessage, CancellationToken cancellationToken)
   at k8s.LineSeparatedHttpContent.CancelableStream.ReadAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at System.IO.StreamReader.ReadBufferAsync(CancellationToken cancellationToken)
   at System.IO.StreamReader.ReadLineAsyncInternal(CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
2025-08-08T22:00:07.1642967+00:00 xUnit.net INFORMATION   Finished:    Miller.WMS.Edge.Tests
2025-08-08T22:00:07.1689229+00:00 xUnit.net INFORMATION === TEST EXECUTION SUMMARY ===
2025-08-08T22:00:07.1732843+00:00 xUnit.net INFORMATION    Miller.WMS.Edge.Tests  Total: 8, Errors: 0, Failed: 8, Skipped: 0, Not Run: 0, Time: 75.771s
