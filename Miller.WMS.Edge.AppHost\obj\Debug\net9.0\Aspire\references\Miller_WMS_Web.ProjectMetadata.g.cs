// <auto-generated/>

namespace Projects;

[global::System.CodeDom.Compiler.GeneratedCode("Aspire.Hosting", null)]
[global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage(Justification = "Generated code.")]
[global::System.Diagnostics.DebuggerDisplay("Type = {GetType().Name,nq}, ProjectPath = {ProjectPath}")]
public class Miller_WMS_Web : global::Aspire.Hosting.IProjectMetadata
{
    public string ProjectPath => """C:\_\Miller_Github\Miller.WMS\Miller.WMS.Web\Miller.WMS.Web.csproj""";
}
