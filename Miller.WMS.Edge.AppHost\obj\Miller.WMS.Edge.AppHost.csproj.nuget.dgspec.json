{"format": 1, "restore": {"C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.Edge.AppHost\\Miller.WMS.Edge.AppHost.csproj": {}}, "projects": {"C:\\_\\Miller_Github\\Miller.WMS\\Miller.WMS.Edge.AppHost\\Miller.WMS.Edge.AppHost.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Edge.AppHost\\Miller.WMS.Edge.AppHost.csproj", "projectName": "Miller.WMS.Edge.AppHost", "projectPath": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Edge.AppHost\\Miller.WMS.Edge.AppHost.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_\\<PERSON>_<PERSON>\\Miller.WMS\\Miller.WMS.Edge.AppHost\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Aspire.Dashboard.Sdk.win-x64": {"target": "Package", "version": "[9.4.0, )", "autoReferenced": true}, "Aspire.Hosting.AppHost": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.AppConfiguration": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.AppContainers": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.ApplicationInsights": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.CosmosDB": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.EventHubs": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.KeyVault": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.PostgreSQL": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.Redis": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.Search": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.ServiceBus": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.Sql": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Azure.Storage": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Keycloak": {"target": "Package", "version": "[9.3.1-preview.1.25305.6, )"}, "Aspire.Hosting.MongoDB": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.MySql": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Orchestration.win-x64": {"target": "Package", "version": "[9.4.0, )", "autoReferenced": true}, "Aspire.Hosting.PostgreSQL": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.RabbitMQ": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Redis": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.SqlServer": {"target": "Package", "version": "[9.4.0, )"}, "CommunityToolkit.Aspire.Hosting.Java": {"target": "Package", "version": "[9.7.0, )"}, "CommunityToolkit.Aspire.Hosting.MongoDB.Extensions": {"target": "Package", "version": "[9.7.0, )"}, "CommunityToolkit.Aspire.Hosting.PostgreSQL.Extensions": {"target": "Package", "version": "[9.7.0, )"}, "CommunityToolkit.Aspire.Hosting.Redis.Extensions": {"target": "Package", "version": "[9.7.0, )"}, "CommunityToolkit.Aspire.Hosting.SqlServer.Extensions": {"target": "Package", "version": "[9.7.0, )"}, "Elastic.Aspire.Hosting.Elasticsearch": {"target": "Package", "version": "[9.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}